"use client";

import { useRef, useEffect, useState, useTransition } from "react";
import { DefaultChatTransport } from 'ai';
import {
  useChat,
  useCompletion,
  type UIMessage,
} from '@ai-sdk/react';
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from 'uuid'
import { useUser } from "@clerk/nextjs";
import { deleteMessageAction } from "@/app/actions";
import { 
  buildGoogleGenAIHistory,
} from '@/lib/buildGoogleGenAIPrompt'

import { ChatHeader } from "@/components/chat-header";
import { 
  Companion, 
  Thread, 
  Persona,
  ChatSummary, 
  Observations, 
  Message as chatMessage, 
  Todos,Note } from '@prisma/client'
import { 
  saveIsVisitedToDatabase,
  savePersonaToDatabase
} from "@/lib/databaseUtils";
import va from "@vercel/analytics";
import clsx from "clsx";
import toast from "react-hot-toast";
import ChatSummaryNew from '@/components/summary/ChatSummaryNew'
import NoteNew from '@/components/note/NoteNew'
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs"
import { 
  Tooltip, 
  TooltipContent, 
  TooltipTrigger
} from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { GitMerge, SendHorizontal, Trash2, Settings } from "lucide-react";
import { MarkMapModalX } from "@/components/modals/MarkMapModalX";
import { ChatList } from '@/components/chat-list'
import { TalkForm } from '@/components/talk/talk-form'
import { ChatScrollAnchor } from '@/lib/hooks/chat-scroll-anchor'
import { GradientPicker } from "@/components/GradientPicker.tsx"
import { ProfileList } from '@/components/profile-list'
import { MarkMap } from '@/components/markmap/markmap'
import { useBackground } from '@/components/providers/BackgroundContext';


type ThreadInfo = { id: string; name: string } | null;

interface IntakeClientProps {
  companion: Companion & {
    messages: chatMessage[]
    chatSummaries: ChatSummary[]
    _count: {
      messages: number
    }
  };
  threadId: string
  existingThread: ThreadInfo
  foundPersona: Persona | null
  notes: Note[]
};

interface MyPersona {
  currentRole: string | null,
  name?: string,
  nickName: string | null,
  gender: string | null,
  age: number | null,
  traits?: string | null,
  status?: string | null,
  isVisited: boolean,
}

const examples = [
  "",
  "",
  "",
];


async function apiRouteChoice(companionId: string) {
  let apiRoute: string;

  switch (companionId) {
    case 'c98b5fe0-e1c9-4c02-8d84-01cb1d0092e3': //Counselor
      apiRoute = `/api/intake/${companionId}/youth`;
      break;
    default:
      // Handle the case for an unknown ai_name or set a default URL
      apiRoute = `/api/intake/${companionId}`;
      break;
  }

  return {
    apiRoute
  };
}

export function IntakeClient({
    companion,
    threadId,
    existingThread,
    foundPersona,
    notes,
  }: IntakeClientProps) {
  const router = useRouter();
  const { user } = useUser()
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [chatRuns, setChatRunCount] = useState<number>(1)
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(9)
  const [chatMessages, setChatMessages] = useState(companion.messages);
  const [chatHistoryMessages, setChatHistoryMessages] = useState<any[]>([]);
  const [chatSummary, setChatSummary] = useState(companion.chatSummaries);
  const [chatSummaryList, setChatSummaryList] = useState<string[]>([]);
  const [note, setNote] = useState<Note[]>(notes);
  const updateInProgressRef = useRef(false);
  const [triggerUpdate, setTriggerUpdate] = useState(false);
  const [useMarkMap, setUseMarkMap] = useState("");
  const { background, setBackground } = useBackground();
  const [useApiRouteTarget, setUseApiRouteTarget] = useState(`/api/intake/${companion.id}`);
  const latestThreadId = useRef<string | null>(null)
  const path = `/intake/${companion.id}/${threadId}`
  const newChatPath = `/intake/${companion.id}`

  console.log("threadId: ", threadId)

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const fetchApiRoute = async () => {
      try {
        const result = await apiRouteChoice(companion.id!);
        setUseApiRouteTarget(result.apiRoute);
      } catch (error) {
        console.error('Error fetching API route:', error);
      }
    };

    fetchApiRoute();
  }, [companion.id]);

  useEffect(() => {
    console.log("useApiRouteTarget: ", useApiRouteTarget)
  }, [useApiRouteTarget]);

  useEffect(() => {   
    if (companion.messages.length === 0) {
      // Trigger the 'append' function with the "Hello" message when the page loads
      const helloMessage: UIMessage = {
        id: companion.id,
        parts: [{ type: 'text', text: `您好，我是${companion.name}` }],
        role: 'system',
      };

      sendMessage(helloMessage)
    }
  }, []);

  useEffect(() => {
    if (foundPersona) {
      setUsePersona(foundPersona);
      const newValue = `# Status
- ${foundPersona.status!}
`
      setUseMarkMap(newValue)
    }
    console.log("foundPersona@intake: ", foundPersona)
  }, [foundPersona]);

  useEffect(() => {
    setChatMessages(companion.messages);
  }, [companion.messages]);

  useEffect(() => {
    setChatSummary(companion.chatSummaries)
  }, [companion.chatSummaries])

  useEffect(() => {
    if (chatSummary.length > 0 && chatRuns <= 1) {
      const latestChatSummaries = companion.chatSummaries
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(latestChatSummaries);
    } else {
      const latestChatSummary = companion.chatSummaries.slice(-1)
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(prevChatSummaryList => [...prevChatSummaryList, ...latestChatSummary]);
    }
  }, [chatSummary, chatRuns]);

  useEffect(() => {
    const latestMessages = companion.messages.slice(-10, -1)
      .map(message => ({
        id: message.id,
        role: message.role,
        parts: [{ type: 'reasoning' as const, text: message.content }]
      }));

    const latestGeminiAiMessages = buildGoogleGenAIHistory(latestMessages)

    setChatHistoryMessages(latestGeminiAiMessages.history)
  }, [companion.messages])

  useEffect(() => {
    setNote(notes)
  }, [notes])

  const incrementChatRunCount = () => {
    setChatRunCount((prevCount) => prevCount + 1);
  };

  const {
    complete, 
    completion
  } = useCompletion({
    api: `/api/intake/${companion.id}/summary`,
    body: {      
      threadId,
      userName: usePersona?.nickName ?? usePersona?.name,
      companionName: companion.name,
      chatHistoryMessages,
      path,
    },
    /*onResponse: res => {
      if (res.status === 429) {
        console.error('You are being rate limited. Please try again later.')
      }
    },*/
    onFinish(_prompt, completion) {
      console.log("completion", completion);
    },
  });

  const [input, setInput] = useState('');
  const [data, setData] = useState<any | undefined>(undefined)

  const {
    messages,
    sendMessage,
    status
  } = useChat({
    //messages: chatMessages,
    transport: new DefaultChatTransport({
      api: useApiRouteTarget,
      body: () => ({
        chatRuns,
        companion,
        threadId,
        existingThread,
        chatSummaryList,
        useReflectionThreshold,
        persona: {
          currentRole: usePersona?.currentRole,
          name: usePersona?.name,
          nickName: usePersona?.nickName,
          age: usePersona?.age,
          gender: usePersona?.gender,
          traits: usePersona?.traits,
          status: usePersona?.status,
          isVisited: usePersona?.isVisited
        },
      }),      
    }),
    /*onResponse: (response) => {
      if (response.status === 429) {
        toast.error("You have reached your request limit for the day.");
        va.track("Rate limited");
        return;
      } else {
        va.track("Chat initiated");
      }

      // Get the latest message id from the server
      const newThreadId = response.headers.get('X-New-Thread-Id')
      latestThreadId.current = newThreadId
      console.log("newThreadId@Intake: ", newThreadId)

    },*/

    onError: (error) => {
      //toast.error(error.message)
      va.track("Chat errored", {
        input,
        error: error.message,
      });
    },

    onFinish: async ({ message }) => {
      incrementChatRunCount() 
      console.log("onFinish@intake: ", message)

      if (threadId && latestThreadId.current !== threadId) {
        const newThreadId = threadId
        latestThreadId.current = newThreadId
        router.refresh() // for getThreads update
      }

      //if (chatRuns % (useReflectionThreshold) === 10000) {
      //  const chatSummaryTemplate = `檢視我們的聊天紀錄，並總結關於我的資訊。`
        //complete(chatSummaryTemplate)
      //}
    },


  });


  let personaData: MyPersona | undefined = undefined;

  if (data !== null && data !== undefined && data.length > 0) {
    const updatedData: MyPersona = data[0] as unknown as MyPersona;
    console.log("updatedData@inTake: ", updatedData);

    personaData = {
      currentRole: updatedData.currentRole,
      nickName: updatedData.nickName,
      gender: updatedData.gender,
      age: updatedData.age,
      isVisited: updatedData.isVisited,
    };
    console.log("personaData1@inTake: ", personaData);
  }


  useEffect(() => {
    if (foundPersona && personaData !== undefined && personaData !== null && !triggerUpdate) {
      const updatedPersona: Persona = {
        ...foundPersona,
        ...personaData,
      };

      setUsePersona(updatedPersona);
      setTriggerUpdate(true)

    console.log("Updated Persona@inTake: ", updatedPersona);
    console.log("personaData@inTake: ", personaData)
  }
}, [foundPersona, personaData, triggerUpdate]);


  console.log("data@inTake: ", data)
  console.log("messages@inTake: ", messages)

  if (!isMounted) {
    return null;
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage({ text: input });
    setInput('');
  };
  const isLoading =  status !== 'ready' && status !== 'error';
  const disabled = isLoading || input.length === 0;

  return (
    <div className="flex flex-row items-center justify-between max-w-screen max-h-screen overflow-hidden"
      style={{ background }}
    >
      {/* Left Panel */}
      <div className="hidden lg:block md:flex w-[48%] flex-grow mt-[1rem] px-1 overflow-x-overlay overflow-y-hidden">          
        <div className="flex flex-col h-[60%] pr-2 justify-center overflow-y-hidden">
        {useMarkMap && (
          <>            
            <MarkMapModalX
              src={useMarkMap as string}
            />
            <MarkMap initialValue={useMarkMap} />
          </>
        )}
        </div>
      </div>
      {/* Center Panel */}
      <div className="flex flex-col w-full items-center justify-between pt-[3rem]">
        <div className="fixed top-[-1px] z-50 rounded-b-2xl lg:w-[47%]">
          <ChatHeader companion={companion} />
        </div>
        <div className="flex-grow h-[86vh] w-full pb-[100px] mt-1 overflow-x-hidden overflow-y-auto">
          {messages.length ? (
            <>
              <ChatList 
                companion={companion}
                isLoading={isLoading}
                messages={messages}
                path={path}
              />
            <ChatScrollAnchor trackVisibility={isLoading} />
            </>
          ) : (
          <div className="border-gray-200 sm:mx-0 mx-5 mt-2 rounded-md border sm:w-full">
          <div className="flex flex-col space-y-4 p-7 sm:p-10">
              <h1 className="text-lg font-semibold text-primary-text">
                Welcome to ComfyMinds Lab
              </h1>
              <p className="text-primary-text">
                {companion.introduction}
              </p>
          </div>
          <div className="flex flex-col space-y-4 border-t border-gray-0 p-1 sm:p-10">
              {examples.map((example, i) => (
              <button
                  key={i}
                  className="rounded-md border border-gray-0 bg-primary/10 px-5 py-1 text-left text-sm text-gray-500 transition-all duration-75 hover:border-black hover:text-gray-700 active:bg-gray-50"
                  onClick={() => {
                  setInput(example);
                  inputRef.current?.focus();
                  }}
              >
                  {example}
              </button>
              ))}
          </div>
          </div>
          )}
        </div>
        <div className="fixed bottom-0 flex w-full lg:w-[50%] flex-col items-center space-y-0 lg:px-5 py-2 pb-0 sm:px-0">
        <TalkForm
          handleSubmit={handleSubmit}
          input={input}
          setInput={setInput}
          isLoading={isLoading}
          disabled={disabled}
          path={newChatPath}
          formWidth={'100%'}
        />
        </div>
      </div>
      {/* Settings 
      <div className="flex flex-col space-y-4">
        {messages.length > 0 && (
          <button
            disabled={isPending}
            className={`relative flex h-9 w-9 items-center justify-center rounded-md transition-all ${
              isPending ? "bg-gray-400" : "bg-red-500"
            }`}
            onClick={() =>
              startTransition(async () => {
                await deleteMessageAction({
                  companionId: companion.id,
                  path: `/intake/${companion.id}`,
                });
              })
            }
          >
            <Trash2
              className={clsx("h-6 w-6", isPending ? "bg-gray-400" : "bg-red-500")}
            />
          </button>
        )}
        {usePersona && usePersona?.isVisited === true && (
          <button
            disabled={isPending}
            className={`relative flex h-9 w-9 items-center justify-center rounded-md transition-all ${
              isPending ? "bg-gray-400" : "bg-teal-500"
            }`}
            onClick={() =>
              startTransition(async () => {
                await savePersonaToDatabase(user!.id, "", "", "", 0, false);
                setChatRunCount(1);
                router.refresh();
              })
            }
          >
            <Settings
              className={clsx(
                "h-6 w-6",
                isPending ? "bg-gray-400" : "bg-teal-500"
              )}
            />
          </button>
        )}
      </div>
      */}
      {/* Right Panel */}
      <div className="hidden lg:flex flex-col h-[100vh] 2xl:h-[100vh] w-[42%] lg:w-[42%] pt-8 px-1 overflow-hidden">
        <div className="flex flex-col pr-2 justify-center">
          <Tabs defaultValue="chatSummary" className="flex flex-col items-center pt-3 mt-7 bg-transparent">
            <TabsList className="bg-background/70">
            <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="chatSummary">Summary</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>
            <TabsContent value="theme">
              <div className="w-full h-[3rem] flex justify-center items-center rounded !bg-cover !bg-center transition-all gap-1">
              <GradientPicker />
              </div>
            </TabsContent>
            <TabsContent value="chatSummary">
              {chatSummary.length === 0 && (
                <ProfileList usePersona={usePersona!} path={path}/>
              )}
              <ChatSummaryNew chatSummary={chatSummary} />
            </TabsContent>
            <TabsContent value="notes">
              <NoteNew note={note} path={path}/>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}