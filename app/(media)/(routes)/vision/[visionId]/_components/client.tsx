"use client";

import { FormEvent, useRef, useEffect, useState, useTransition } from "react";
import { DefaultChatTransport } from 'ai';
import { useChat, type UIMessage } from '@ai-sdk/react';
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import Image from "next/image";
import Gallery from '@/components/gallery/gallery';
import sample from "lodash/sample";
import { updateGalleryAction } from "@/app/actions/cldUploadActions"
import { deleteMessageAction } from "@/app/actions";

import { Companion, Role, Message as chatMessage, Media } from '@prisma/client'
import va from "@vercel/analytics";
import clsx from "clsx";
import { cn } from '@/lib/utils'
import { ArrowUp, XCircle, Paperclip, ImagePlus, Trash2 } from "lucide-react";
import { RiSketching } from "react-icons/ri";
import { LoadingCircle } from '@/components/loaders'
import { BarLoader} from  'react-spinners'
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import Textarea from "react-textarea-autosize";
import toast from "react-hot-toast";
import { ChatHeader } from "@/components/chat-header";
import { ImageList } from '@/components/image-list';
import { CldUploadButton } from "next-cloudinary";
import { SketchModal } from "@/components/modals/SketchModal";
import { ChatScrollAnchor } from '@/lib/hooks/chat-scroll-anchor'
import { Button, buttonVariants } from '@/components/ui/button'
import { IconPlus } from '@/components/ui/icons'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/canvas-dialog'
import { CardBody, CardContainer, CardItem } from "@/components/ui/3d-card";
import { PromptIdea } from './PromptIdea'

type ThreadInfo = { id: string; name: string } | null;

interface VisionClientProps {
  companion: Companion & {
    messages: chatMessage[];
    medias: Media[];
  };
  threadId: string
  existingThread: ThreadInfo
};

interface ImageData  {
  type: string;
  content: string;
  url: string;
  numOfImage?: number;
}

const examples: string[] = [
  "",
];

const samplePrompts = [
  "a gentleman otter in a 19th century portrait",
  "bowl of ramen in the style of a comic book",
  "flower field drawn by Jean-Jacques Sempé",
  "illustration of a taxi cab in the style of r crumb",
  "multicolor hyperspace",
  "painting of fruit on a table in the style of Raimonds Staprans",
  "pencil sketch of robots playing poker",
  "photo of an astronaut riding a horse",
];

export function VisionClient({
    companion,
    threadId,
    existingThread,
  }: VisionClientProps) {
  const router = useRouter();
  const { user } = useUser()
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [isPending, startTransition] = useTransition();
  const [medias, setMedias] = useState(companion ? companion.medias : []);
  //const medias: Media[] = companion ? companion.medias : [];
  const [chatMessages, setChatMessages] = useState(companion ? companion.messages : []);
  const [chatRuns, setChatRunCount] = useState<number>(1)
  const [selectedMediaUrl, setSelectedMediaUrl] = useState<string | null>(null);
  const uploadInProgressRef = useRef(false);
  const [cldUploadCount, setCldUploadCount] = useState<number>(0)
  const [triggerUpdate, setTriggerUpdate] = useState(false);
  const [chatWithVisionImage, setChatWithVisionImage] = useState<string>("");
  const [formData, setFormData] = useState<FormData>(new FormData());
  const [captionMode, setCaptionMode] = useState<string>("");
  const [imageFile, setImageFile] = useState<Blob | null >(null);
  const [maskFile, setMaskFile] = useState<File | null >(null);
  const [sketchModalOpen, setSketchModalOpen] = useState(false)
  const handleSelectMedia = (mediaUrl: string) => {
    setSelectedMediaUrl(mediaUrl);
  };
  const [imageTempData, setImageTempData] = useState<ImageData[]>([]);
  const [prompt] = useState(sample(samplePrompts));
  const [generatedPrompts, setGeneratedPrompts] = useState<string[]>([]);
  const latestThreadId = useRef<string | null>(null)

  const incrementChatRunCount = () => {
    setChatRunCount((prevCount) => prevCount + 1);
  };

  const incrementCldUploadCount = () => {
    setCldUploadCount((prevCount) => prevCount + 1);
  };

  const path = `/vision/${companion.id!}/${threadId}`
  const newChatPath = `/vision/${companion.id}`

  const handleUpload = (result: any) => {
    const chatWithVisionImageUrl: string = result.info.secure_url
    setChatWithVisionImage(chatWithVisionImageUrl)
    startTransition(async () => {
      await updateGalleryAction({
        companionId: companion.id,
        imageUrl: chatWithVisionImageUrl,
        path
      })
    })
  }

  let receivedFormData: FormData | null = null;

  useEffect(() => {
    setChatMessages(companion.messages)
  }, [companion.messages])

  useEffect(() => {
    setMedias(companion.medias)
  }, [companion.medias])

  console.log("VisionClient: companion =", companion);
  console.log("VisionClient: medias =", companion.medias);

  useEffect(() => {
    console.log("VisionClient: captionMode =", captionMode);
    console.log("VisionClient: maskFile =", maskFile);
    console.log("selectedMediaUrl", selectedMediaUrl)
  }, [captionMode, maskFile, selectedMediaUrl])


  useEffect(() => {
    if (formData) {
      const receivedFormData = new FormData();
  
      for (const [key, value] of formData.entries()) {
        receivedFormData.append(key, value);
      }

      const mode = receivedFormData.get("mode");
      const image = receivedFormData.get("image");
      const mask = receivedFormData.get("mask");
      const amount = receivedFormData.get("n") as string;
      const size = receivedFormData.get("size");
      const count = amount !== null ? parseInt(amount, 10) : 1;

      if (mode) {
        setCaptionMode(mode as string)
      }

      if (image) {
        setImageFile(image as Blob)
      }
      if (mask) {
        setMaskFile(mask as File)
      }
      
      

      console.log("VisionClient: mode =", mode);
      console.log("VisionClient: image =", image);
      console.log("VisionClient: mask =", mask);
      console.log("VisionClient: count =", count);
      console.log("VisionClient: size =", size);
      console.log("VisionClient: amount =", amount);
    }
    console.log("formData@VisionClient: ", formData)
  }, []) //[formData]


  // Function to handle the generated prompts
  const handleGeneratedPrompts = (prompts: string[]) => {
    console.log('Generated prompts:', prompts);
    setGeneratedPrompts(prompts); // You can use the generated prompts as needed
    examples.push(generatedPrompts.join(', '));
  };

  /*useEffect(() => {
    // Trigger the 'append' function with the "Hello" message when the page loads
    const helloMessage: Message = {
      id: companion.id,
      content: `您好，我是${companion.name}，歡迎來到繪畫園地`,
      role: 'system',
    };

    append(helloMessage)
  }, []);*/

  const resetFormData = () => {
    setFormData(new FormData());
  };

  const [input, setInput] = useState('');
  const [data, setData] = useState<any | undefined>(undefined)

  const {
    messages,
    sendMessage,
    status
  } = useChat({
    //messages: chatMessages,
    transport: new DefaultChatTransport({
      api: `/api/vision/${companion.id}/gemini`,
      body: () => ({
        chatRuns,
        companion,
        threadId,
        existingThread
      })
    }),
    /*onResponse: (response) => {
      if (response.status === 429) {
        toast.error("You have reached your request limit for the day.");
        va.track("Rate limited");
        return;
      } else {
        va.track("Chat initiated");
      }
    },*/

    onError: (error) => {
      //toast.error(error.message)
      va.track("Chat errored", {
        input,
        error: error.message,
      });
    },

    onFinish: async ({ message }) => {
      //console.log("message: ", message)
      //resetFormData()
      if (cldUploadCount > 0) {
        setCldUploadCount(0)
      }
      
      setChatWithVisionImage("")
      incrementChatRunCount() 
      
      if (threadId && latestThreadId.current !== threadId) {
        const newThreadId = threadId
        latestThreadId.current = newThreadId
        router.refresh() // for getThreads update
      }
    },
  });

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage({ text: input });
    setInput('');
  };

  const isLoading = status !== "ready" && status !== "error";

  interface ParsedData {
    data?: {
      url: string;
    }[];
    error?: {
      message: string;
    };
  }

  const generateImage = async () => {
    let parsedData: ParsedData = {};
    try {
      const textDesc = input
      formData.set('prompt', textDesc);
      if (formData !== null) {
        const requestOptions = {
          method: 'POST',
          body: formData,
        };
        const response = await fetch('/api/image/dalle', requestOptions);

        parsedData = await response.json();
        console.log("parsedData", parsedData);
      }
    } catch (error: any) {
      if (error?.response?.status === 400) {
        const errorMessage = error?.response?.data?.message || "Bad Request";
        toast.error(errorMessage);
      } else {
        toast.error("Something went wrong.");
      }   
    } finally {
      setCaptionMode("")
    }
    if (
      Array.isArray(parsedData) &&
      parsedData.length > 0 &&
      parsedData[0].url !== undefined &&
      parsedData[0].url !== "" &&
      parsedData[0].url !== null
    ) {
      let imageUrl = parsedData[0].url;
      console.log("imageUrl: ", imageUrl)
    } else if (parsedData.error !== null && parsedData.error !== undefined) {
      console.log(parsedData.error.message);
    }
  }

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage(
      { role: 'user', parts: [{ type: 'text', text: input }] },
      { body: { chatWithVisionImageUrl: chatWithVisionImage } },
    );
     setInput('')

    //setTriggerUpdate(true)
  
    // Get the form input value
    /*const message = input;
  
    // Create a new FormData object
    const formData = new FormData();
  
    // Append the imageFile and maskFile to the FormData
    if (imageFile && maskFile) {
      formData.append("imageFile", imageFile);
      formData.append("maskFile", maskFile);
    }

    formData.append("mode", "edit");
    formData.append("size", "512x512");
    formData.append("n", "1");
    formData.append("prompt", message);*/

  
    // Create the chatRequestOptions object
    /*const chatRequestOptions: any = {
      options: {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        body: formData,
      },
    };
  
    // Call the append function from the Vercel AI SDK
    const response = await append(chatRequestOptions);
  
    // Handle the response
    console.log(response);*/
  };



  console.log("data@vision: ", data)

  const cldUploadImage = async (
    imageToUpload: ImageData[],
    imageVisionId: ImageData[]
  ) => {
    // Ensure the upload isn't already in progress
    if (uploadInProgressRef.current) {
      return;
    }
    uploadInProgressRef.current = true;

    
    try {
      let visionMessageId = ''; 
      for (const visionId of imageVisionId) {
        if (visionId.type === 'visionId') {
          visionMessageId = visionId.content;
          break; // Exit loop once found
        }
      }

      const response = await fetch(`/api/visionUpload/${companion.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageToUpload, visionMessageId }),
      });
    
      if (response.ok) {
        const cloudinary_resp = await response.json();
        console.log("cloudinary_resp@vision", cloudinary_resp)
        //setTriggerUpdate(true)

      } else {
        throw new Error('Failed to fetch data from the server');
      }
    } catch (error) {
      console.error('Error uploading images:', error);
    } finally {
      uploadInProgressRef.current = false;
      setTriggerUpdate(false)
    }
  };

  //let imageData: ImageData[] = [];


  if (data !== null && data !== undefined && data.length > 0 && cldUploadCount === 0) {
    setTriggerUpdate(true)
    const imageData = data as unknown as ImageData[]
    console.log("imageData@Vision: ", imageData)

    const numOfImageData: ImageData[] = imageData.filter(item => item.type === 'numOfImage');
    const latestImageData: number = numOfImageData[numOfImageData.length - 1]?.numOfImage!
    
    const imageToUpload: ImageData[] = imageData
      .filter(item => item.type === 'image')
      .slice(-latestImageData);
    const imageVisionId: ImageData[] = imageData
      .filter(item => item.type === 'visionId')
      .slice(-1);
    setImageTempData(imageToUpload)

    console.log("imageToUpload@Vision: ", imageToUpload)
    console.log("imageVisionId@Vision: ", imageVisionId)
    incrementCldUploadCount()
    /*if (!uploadInProgressRef.current) {
      startTransition(async() => {
        incrementCldUploadCount()
        await cldUploadImage(imageToUpload, imageVisionId)
      })
      
    }*/
    setTriggerUpdate(false)
  }

  console.log("initialMessages@Vision: ", companion.messages!)
  console.log("messages@Vision: ", messages)




  const disabled = isLoading || isPending || input.length === 0 || triggerUpdate;
  const dynamicButtonClass = chatWithVisionImage.length > 0 ? 'left-[2.5rem]' : 'left-[0.75rem]'

  return (
    <div className="flex flex-row items-start justify-between max-w-screen max-h-screen overflow-hidden">
      {/* Left Panel */}
      <div className="hidden lg:block md:flex w-[30%] flex-grow pt-[0rem] px-1 overflow-x-overlay overflow-y-hidden">        
        <div className="flex flex-col h-full pr-2 justify-center overflow-y-hidden">
        {chatWithVisionImage && chatWithVisionImage.length > 0 && (
          <div className="absolute top-20 space-y-12 bg-opacity-85">
            <div className="mt-11 mx-4 flex flex-col rounded gap-y-8 bg-transparent">
              <div className="border border-teal-900/10 bg-[#fff0f5] rounded p-2">
                <h2 className="
                text-base
                text-center
                font-semibold
                leading-7
                text-gray-900
                "
                >
                </h2>
                <div className="mx-0 my-0">
                  <CardContainer className="inter-var">
                    <CardBody className="bg-gray-50 relative group/card dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-black dark:border-white/[0.2] border-black/[0.1] w-auto h-auto rounded-xl p-6 border">
                      <CardItem translateZ="100" className="mx-0 my-0">
                        <Image
                          alt="Image"
                          width="155"
                          height="155"
                          sizes="(max-width: 512px) 100vw,
                            (max-width:512px) 50vw,
                            33vw"
                          src={chatWithVisionImage}
                          className="
                          object-cover
                          scale-110
                          transition
                          translate
                          rounded
                          "
                        />
                      </CardItem>
                    </CardBody>
                  </CardContainer>
                  <p className="my-1 mt-4 text-sm text-center leading-7 text-gray-600">
                    Talk about this image
                  </p>
                </div> 
                <div className='absolute top-[2rem] right-[0.2rem] items-center rounded-full bg-transparent z-20'>
                  <div className='mt-0'>
                    <button
                      className="flex items-center rounded-full cursor-pointer bg-secondary p-1"
                      onClick={() =>  {
                        setChatWithVisionImage("")
                      }}
                    >
                      <XCircle color="cyan" className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                <div className="mt-0 flex flex-col gap-y-8">
                </div>
              </div>
            </div>
            <div className="mt-4 flex items-center justify-end gap-x-6">
            </div>
          </div>
        )}
        </div>
      </div>
      {/* Center Panel */}
      <div className="flex flex-col h-[98vh] 2xl:h-screen 2xl:w-[70%] w-full pl-2">
        <ChatHeader companion={companion} />
        <div className="flex-grow h-[100vh] w-[100%] pb-[100px] overflow-x-hidden overflow-y-auto">
        {messages.length ? (
          <>
            <ImageList 
              companion={companion}
              isLoading={isLoading}
              messages={messages}
              imageData={imageTempData}
              formData={formData} 
              setFormData={setFormData}
              selectedMediaUrl={selectedMediaUrl}
              setChatWithVisionImage={setChatWithVisionImage}
            />
            <ChatScrollAnchor trackVisibility={isLoading} />
          </>
        ) : (
          <div className="border-gray-200 sm:mx-0 mx-5 mt-2 rounded-md border sm:w-full">
            <div className="flex flex-col space-y-4 p-7 sm:px-10">
                <h1 className="text-lg font-semibold text-primary-text">
                  Welcome to ComfyMinds Lab
                </h1>
                <p className="text-primary-text">
                  {/* {companion.introduction}*/}
                </p>
            </div>
              <PromptIdea onGeneratePrompts={handleGeneratedPrompts} />
            <div className="flex flex-col space-y-4 border-t border-gray-0 p-1 sm:p-10">
              {examples.map((example, i) => (
                <button
                  key={i}
                  className="rounded-md border border-gray-0 bg-primary/10 px-5 py-1 text-left text-sm text-primary-text transition-all duration-75 hover:border-black hover:text-gray-700 active:bg-gray-50"
                  onClick={() => {
                    const imagePromptExample = `Generate image with style of ${example}`
                    setInput(imagePromptExample);
                    inputRef.current?.focus();
                  }}
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        )}
        </div>
        {isLoading || isPending || triggerUpdate || uploadInProgressRef.current ? (
        <div className="flex justify-center items-center z-20">
          <BarLoader color={'#36d7b7'}  />
        </div>
      ) : null}
        <div className="h-[80px]"></div>
        <div className="fixed bottom-0 left-[50%] grid w-full max-w-3xl 2xl:max-w-4xl translate-x-[-50%] space-y-0 p-5 py-2 pb-0 sm:px-0">
          <div className="flex">
            {/* Tools on the left side of the texarea box*/}
            <div className='flex'>
              <Dialog modal={false} open={sketchModalOpen} onOpenChange={setSketchModalOpen} >
                <DialogTrigger asChild>
                  <button
                    className="flex items-center rounded-full cursor-pointer bg-transparent p-1"
                  >
                    <RiSketching size={30} className="text-sky-500" />
                  </button>
                </DialogTrigger>
                <DialogContent
                  onInteractOutside={(e: any) => e.preventDefault()}
                  className="max-w-[45rem] h-[42rem]"
                >
                  <SketchModal
                    companion={companion}
                    setChatWithVisionImage={setChatWithVisionImage}
                    path={path}
                  />
                </DialogContent>
              </Dialog>
            </div>
            {chatWithVisionImage && chatWithVisionImage.length > 0 ? (
              <div className="flex flex-col h-full justify-center overflow-y-hidden">
                <div className="absolute top-[1.6rem] left-[3.4rem] w-9 h-9 z-20">
                  <Image
                    alt="Image"
                    width="45"
                    height="45"
                    sizes="(max-width: 512px) 100vw,
                            (max-width:512px) 50vw,
                            33vw"
                    src={chatWithVisionImage}
                    className="object-cover transition translate rounded"
                  />
                </div> 
                <div className='absolute top-[1.2rem] left-[4rem] items-center rounded-full bg-transparent z-30'>
                  <div className='mt-0'>
                    <button
                      className="flex items-center rounded-full cursor-pointer bg-transparent p-0"
                      onClick={() =>  {
                        setChatWithVisionImage("")
                      }}
                    >
                      <Paperclip color="black" className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <CldUploadButton 
                options={{ maxFiles: 1 }} 
                onUpload={handleUpload} 
                uploadPreset="wfoehym3"
              >
                <ImagePlus size={30} className="text-sky-500" />
              </CldUploadButton>
            )}
            <form
              ref={formRef}          
              onSubmit={onSubmit}
              className="relative w-full rounded-md bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 ... drop-shadow-lg px-4 pb-2 pt-3 mx-1 shadow-lg sm:pb-3 sm:pt-4"
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={e => {
                      e.preventDefault()
                      router.refresh()              
                      if (newChatPath) {
                        router.push(newChatPath)
                      } else {
                        router.push("/")
                      }
                    }}
                    className={cn(
                      buttonVariants({ size: 'sm', variant: 'outline' }),
                      'absolute', dynamicButtonClass, 'top-4 h-8 w-8 rounded-full bg-secondary p-0 sm:left-[0.9rem]'
                    )}
                  >
                    <IconPlus  />
                    <span className="sr-only">New Chat</span>
                  </button>
                </TooltipTrigger>
                <TooltipContent side="right">New Chat</TooltipContent>
              </Tooltip>
              <Textarea
                ref={inputRef}
                tabIndex={0}
                required
                rows={1}
                autoFocus
                placeholder="Send a message..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    formRef.current?.requestSubmit();
                    e.preventDefault();
                  }
                }}
                spellCheck={false}
                className={`min-h-[30px] w-full ${
                  chatWithVisionImage.length > 0 ? 'pl-[2.7rem]' : 'pl-[2.5rem]'
                } pl-[2.5rem] pr-[3.3rem] resize-none bg-transparent text-white focus-within:outline-none`}
              />
              <button
                className={clsx(
                  "absolute inset-y-0 right-4 my-auto flex h-8 w-8 items-center justify-center rounded-md transition-all",
                  disabled
                    ? "cursor-not-allowed bg-indigo-600"
                    : "bg-green-600 hover:bg-green-700",
                )}
                disabled={disabled}
              >
                {isLoading || isPending || triggerUpdate ? (
                  <LoadingCircle />
                ) : (
                  <ArrowUp size={18}
                    className={clsx(
                      "",
                      input.length === 0 ? "text-gray-300" : "text-white",
                    )}
                  />
                )}
              </button>
              {captionMode !== "" && (
                <button
                  className="rounded-md border border-gray-0 bg-primary/10 px-5 py-1 text-left text-sm text-gray-500 transition-all duration-75 hover:border-black hover:text-gray-700 active:bg-gray-50"
                  onClick={() =>  generateImage()}
                >
                  Test
                </button>
              )}
            </form>
            <p className="text-center text-xs text-gray-400">
            </p>
          </div>
        </div>
      </div>
      {/* 
      {messages.length > 0 && (
        <button
          disabled={isPending}
          className={`relative inset-y-0 my-auto flex h-9 w-9 items-center justify-center rounded-md transition-all ${
            isPending ? "bg-gray-400" : "bg-red-500"
          }`}
          onClick={() =>
            startTransition(async() => {
              await deleteMessageAction({ companionId: companion.id, path: `/vision/${companion.id}` })
            })
          }
        >
          <Trash2
            className={clsx(
              "h-6 w-6",
                isPending ? "bg-gray-400" : "bg-red-500",
            )}
          />
        </button>
      )}
      */}
      {/* Right Panel */}
      <div className="hidden lg:flex flex-col h-[100vh] 2xl:h-[100vh] w-[30%] md:w-[30%] lg:w-[30%] px-1 overflow-hidden">
        <div className="flex flex-col pr-4 justify-center">
          <Gallery 
            companionId={companion.id}
            medias={medias}
            onSelect={handleSelectMedia}
            formData={formData} 
            setFormData={setFormData}
            setChatWithVisionImage={setChatWithVisionImage}
          />
        </div>
      </div>
    </div>
  );
}