import { input } from '@inquirer/prompts';
'use server'

import prismadb from "@/lib/prismadb"
import { AIModel, Transaction } from '@prisma/client'
import { MODEL_COSTS, API_MULTIPLIER, UNIT_AMOUNT } from '@/constants'
import { auth, currentUser } from '@clerk/nextjs/server'
import { stripe } from "@/lib/stripe"
import { 
  schemaPayment, 
  schemaPaymentId 
} from '@/components/credits/forms/schemas'
import { revalidatePath } from 'next/cache'

export async function addTransaction({
  userApiCount,
  inputTokens,
  outputTokens,
  model,
  userId,
  notes,
}: {
  userApiCount: number
  inputTokens: number
  outputTokens: number
  model: AIModel
  userId: string
  notes?: string
}) {
  const COST = MODEL_COSTS[model]

  const usage =
    (promptTokens * COST.promptTokenCost) / 1000 +
    (completionTokens * COST.completionTokenCost) / 1000

  /*const latestTransaction = await prismadb.transaction.findFirst({
    where: { 
      userId,
      amount: { gt: 0 }
    },
    orderBy: { createdAt: 'desc' },
  });*/
 
  await prismadb.creditBalance.upsert({
    where: { userId },
    create: {
      balance: -usage,
      apiBalance: -userApiCount,
      userId,
      Transactions: {
        create: {
          count: -userApiCount,
          amount: -usage,
          inputTokens,
          outputTokens,
          userId,
          model,
          notes,
        },
      },
    },
    update: {
      balance: { decrement: usage },
      apiBalance: { decrement: userApiCount },
      balEquivExpense: { decrement: usage },
      apiEquivExpense: { decrement: userApiCount },
      Transactions: {
        create: {
          count: -userApiCount,
          amount: -usage,
          inputTokens,
          outputTokens,
          userId,
          model,
          notes,
        },
      },
    },
  })
}

export async function addCreditBalance({
  sessionId,
  secret,
  path
}: {
  sessionId: string
  secret: string
  path?: string
}) {
  const { userId } = await auth();
  if (secret !== process.env.APP_SECRET_KEY || !userId) {
    throw new Error("Unauthorized");
  }

  if (!sessionId) {
    throw new Error('Session ID missing.');
  }

  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    const parsedCreditsInfo = schemaPayment.safeParse(session.metadata);
    const parsedPaymentId = schemaPaymentId.safeParse(session.payment_intent);

    if (!parsedCreditsInfo.success || !parsedPaymentId.success) {
      throw new Error('Payload is missing.');
    }

    const { creditsCount, userId } = parsedCreditsInfo.data;
    const paymentIntent = parsedPaymentId.data;

    const creditBalance = await prismadb.creditBalance.upsert({
      where: { userId },
      create: {
        userId,
        balance: creditsCount * UNIT_AMOUNT,
        apiBalance: creditsCount * API_MULTIPLIER,
        Transactions: {
          create: {
            count: creditsCount * API_MULTIPLIER,
            amount: creditsCount * UNIT_AMOUNT,
            userId,
            //paymentId: paymentIntent,
            notes: 'Credits purchased.',
          },
        },
      },
      update: {
        balance: { increment: creditsCount * UNIT_AMOUNT },
        apiBalance: { increment: creditsCount * API_MULTIPLIER },
        balEquivExpense: 0, // reseet equiv bal expense of the cash flow to 0 
        apiEquivExpense:0, // reseet equiv api expense of the cash flow to 0 
        Transactions: {
          create: {
            notes: 'Credits purchased.',
            count: creditsCount * API_MULTIPLIER,
            amount: creditsCount * UNIT_AMOUNT,
            userId,
            //paymentId: paymentIntent,
          },
        },
      },
    });

    if (path) {
      revalidatePath(path)
    }  

    return creditBalance;
  } catch (error) {
    console.error("[CREDIT_BALANCE_UPDATED]", error);
    throw new Error("CreditBalance not updated");
  }
}

export async function transferBalance(
  formData: {
    amount: number;
    notes?: string;
  },  
  toUserIds: string[],
  path?: string) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized");
  }
  console.log("toUserIds: ", toUserIds);
  const { 
    amount, 
    notes 
  } = formData;

  if (!toUserIds || toUserIds?.length === 0) {
    throw new Error("No selected users for transfer");
  }
  try {
    const totalAmount = amount * toUserIds.length;
    
    await prismadb.$transaction(async (tx) => {
       // Check admin balance
       const adminBalance = await tx.creditBalance.findFirst({
         where: { userId },
       });
       if (!adminBalance || adminBalance.apiBalance < totalAmount * API_MULTIPLIER) {
         throw new Error('Insufficient balance for transfer');
       } else {
         // Update admin balance
         await tx.creditBalance.update({
           where: { userId },
           data: {
             balance: { decrement: totalAmount * UNIT_AMOUNT },
             apiBalance: { decrement: totalAmount * API_MULTIPLIER },
             balEquivExpense: 0, // reseet equiv bal expense of the cash flow to 0 
             apiEquivExpense:0, // reseet equiv api expense of the cash flow to 0 
           },
         });
   
         // Create transactions for each member
         for (const toUserId of toUserIds) {
           //const userBalance = await tx.creditBalance.findFirst({
           //  where: { userId: toUserId },
           //});
   
           //if (!userBalance) {
           //  throw new Error(`Credit balance not found for user ${toUserId}`);
           //}
           const userBalance = await tx.creditBalance.upsert({
             where: { userId: toUserId },
             create: {
               userId: toUserId,
               balance: amount * UNIT_AMOUNT,
               apiBalance: amount * API_MULTIPLIER,
             },
             update: { 
              balance: { increment: amount * UNIT_AMOUNT },
              apiBalance: { increment: amount * API_MULTIPLIER },
              balEquivExpense: 0, // reseet equiv bal expense of the cash flow to 0 
              apiEquivExpense:0, // reseet equiv api expense of the cash flow to 0 
            },
           });
   
           await tx.transaction.create({
             data: {
               userId: toUserId,
               count: amount * API_MULTIPLIER,
               amount: amount * UNIT_AMOUNT,
               notes: notes || 'Balance received',
               creditBalanceId: userBalance.id,
             },
           });
         }
   
         // Create transaction for admin
         await tx.transaction.create({
           data: {
             userId,
             count: -totalAmount * API_MULTIPLIER,
             amount: -totalAmount * UNIT_AMOUNT,
             notes: notes || 'Balance transfered',
             creditBalanceId: adminBalance.id,
           },
         });
       }
    });
    if (path) {
      revalidatePath(path)
    }
   } catch (error: any) {
    throw new Error(error.message || 'Transaction failed');
   }
}