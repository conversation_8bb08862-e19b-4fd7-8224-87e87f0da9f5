import React, { JSX, useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import { nanoid } from '@/lib/utils'
import { useUser } from "@clerk/nextjs";
import { DefaultChatTransport } from "ai";
import { type UIMessage, useChat } from "@ai-sdk/react";
import { Bot, Trash, XCircle } from "lucide-react";
import { IconArrowElbow, IconPlus, IconSpinner } from '@/components/ui/icons'
import Image from "next/image";
import Textarea from 'react-textarea-autosize'
import { useEnterSubmit } from '@/lib/hooks/use-enter-submit'
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { 
  Companion, 
  Message as chatMessage, 
  Observations, 
  ChatSummary,
  Todos,
  Note,
  Persona } from '@prisma/client'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { ChatMessageActions } from '@/components/chat-message-actions'
import ImageModal from "@/components/modals/ImageModal";

type ThreadInfo = { id: string; name: string } | null;

interface AIChatBoxProps {
  companion: Companion & {
    messages: chatMessage[]
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  threadId: string
  existingThread: ThreadInfo
  path?: string
  open: boolean;
  onClose: () => void;
}

export default function AIChatBox({ 
  companion,
  threadId,
  existingThread,
  path,
  open, 
  onClose,
  ...props
}: AIChatBoxProps) {
  const router = useRouter();
  const { formRef, onKeyDown } = useEnterSubmit()
  const latestThreadId = useRef<string | null>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const scrollRef = useRef<HTMLDivElement>(null);

  const [input, setInput] = useState('');

  const {
    messages,
    setMessages,
    sendMessage,
    status,
    error
  } = useChat({
    //messages: companion?.messages,
    transport: new DefaultChatTransport({
      api: `/api/notes/${companion.id}/google`,
      body: () => ({
        companion,
        threadId,
        existingThread,
        path,
      }),      
    }),

    onFinish: async ({ message }) => {
      if (threadId && latestThreadId.current !== threadId) {
        const newThreadId = threadId
        latestThreadId.current = newThreadId
        router.refresh() // for getThreads update
      }
    },

  });

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (open) {
      inputRef.current?.focus();
    }
  }, [open]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage({ text: input });
    setInput('');
  };

  const isLoading = status !== "ready" && status !== "error";
  const lastMessageIsUser = messages[messages.length - 1]?.role === "user";

  const handleNewChatButtonClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    if (path) {
      router.push(path);
    } else {
      router.push("/");
    }
  };

  return (
    <div
      className={cn(
        "bottom-0 right-0 w-full max-w-[500px] rounded-md p-1 xl:right-36",
        open ? "fixed" : "hidden",
      )}      
      style={{
        background: 'var(--gradient)',
        zIndex: 60
      }}
    >
      <button onClick={onClose} className="mb-1 ms-auto block">
        <XCircle size={30} />
      </button>
      <div className="flex h-[500px] md:h-[500px] lg:h-[500px] xl:h-[600px] flex-col rounded-md border shadow-xl">
        <div className="mt-3 h-full overflow-y-auto px-3" ref={scrollRef}>
          {messages.map((message) => (
            <ChatMessage message={message} key={message.id} path={path} />
          ))}
          {isLoading && lastMessageIsUser && (
            <ChatMessage
              message={{
                id: nanoid(),
                role: "assistant",
                parts: [{ type: 'text', text: 'Thinking...' }]
              }}
            />
          )}
          {error && (
            <ChatMessage
              message={{
                id: nanoid(),
                role: "assistant",
                parts: [{ type: 'text', text: 'Something went wrong. Please try again.' }]
              }}
            />
          )}
          {!error && messages.length === 0 && (
            <div className="flex h-full items-center justify-center gap-3">
              <Bot />
              Ask the AI a question about your notes
            </div>
          )}
        </div>
        <form onSubmit={handleSubmit} ref={formRef} className="m-3 flex gap-1">
          <Button
            title="New chat"
            variant="outline"
            size="icon"
            type="button"
            onClick={handleNewChatButtonClick}
            disabled={isLoading}
          >
            <IconPlus  />
          </Button>
          <Button
            title="Clear chat"
            variant="outline"
            size="icon"
            type="button"
            onClick={() => setMessages([])}
            disabled={isLoading}
          >
            <Trash />
          </Button>
          <Textarea
            ref={inputRef}
            tabIndex={0}
            onKeyDown={onKeyDown}
            value={input}
            onChange={e => setInput(e.target.value)}
            placeholder="Say something..."spellCheck={false}
            className="min-h-[40px] w-full rounded-md resize-none p-2 focus-within:outline-none sm:text-sm"
          />
          <Button
            type="submit"
            size="icon"
            className="h-9 w-9 shrink-0 my-auto"
            disabled={isLoading || input === ''}
          >
            {isLoading ? (
              <IconSpinner className="mr-2 animate-spin" />
            ) : (
              <IconArrowElbow />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}

function ChatMessage({
  message,
  path,
  ...props
}: {
  message: UIMessage;
  path?: string
}) {
  const { role, parts } = message;
  const content = parts.map((part) => {
    if (part.type === 'text') {
      return part.text;
    }
  }).join('\n');
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const codeBlockRef = useRef<SyntaxHighlighter>(null);
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const { user } = useUser();
  const isAiMessage = role === "assistant";

  useEffect(() => {
    if (selectedImageIndex !== null) {
      setImageModalOpen(true);
      console.log("selectedImageIndex", selectedImageIndex);
    }
  }, [selectedImageIndex]);

  return (
    <div
      className={cn(
        "group flex items-center mb-3",
        isAiMessage ? "me-5 justify-start" : "ms-5 justify-end",
      )}
      {...props}
    >
      {isAiMessage && <Bot className="mr-2 shrink-0" />}
      <p
        className={cn(
          "rounded-md px-3 py-2",
          isAiMessage ? "bg-background" : "bg-secondary/70",
        )}
      >
        <MemoizedReactMarkdown
          className="prose break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0 prose-a:text-pink-600 hover:prose-a:text-pink-500"
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={[rehypeKatex]}
          components={{
            p({ children }) {
              const childArray = React.Children.toArray(children);
          
              const remainingContent = childArray.reduce<{
                images: React.ReactNode[];
                text: (string | JSX.Element)[];
              }>((acc, child) => {
                if (typeof child === 'string') {
                  acc.text.push(child);
                } else if (React.isValidElement(child)) {
                  if (child.type === 'img') {
                    acc.images.push(child);
                  } else if (child.type === 'a') {
                    acc.text.push(
                      <a href={(child as React.ReactElement<any>).props.href} key={acc.text.length}>
                        {(child as React.ReactElement<any>).props.children}
                      </a>
                    );
                  } else {
                    acc.text.push(child);
                  }
                }
                return acc;
              }, { images: [], text: [] });

              const images = childArray.filter(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === "img" &&
                  typeof child !== "string" // Filter out string nodes
              );
              {console.log("childArray@images-map", images)}
              
              const renderedImages = images.map((image, index) => {
                if (typeof image === 'object' && image !== null && 'type' in image && typeof image.type === 'function') {
                  //@ts-ignore
                  const { src, alt } = image.props;
                  {console.log("childArray@image-src", src)}
                  return (
                    <div key={index}>
                      <Image
                        onClick={() => setSelectedImageIndex(index)}
                        src={src as string}
                        alt={alt as string}
                        style={{
                          maxWidth: '160px',
                          maxHeight: '160px',
                        }}
                        className="
                        w-full h-auto
                        object-cover
                        cursor-pointer
                        hover:scale-110
                        transition
                        rounded-lg
                        border-1
                        m-0
                        "
                      />
                      {selectedImageIndex === index && (
                        <ImageModal
                          src={src as string}
                          isOpen={imageModalOpen} // Ensuring imageModalOpen controls the modal visibility
                          onClose={() => setSelectedImageIndex(null)}
                        />
                      )}
                    </div>
                  );
                }
                return null;
              });
       
              return (
                <div>
                  {images.length > 0 && (
                    <div className={`grid grid-cols-${images.length === 2 ? 2 : images.length <= 1 ? 1 : 3} gap-2`}
                    >
                      {renderedImages}
                    </div>
                  )}
                  {/* Displaying text content */}
                  <p className="mt-1 mb-2 text-justify last:mb-0">{remainingContent.text}</p>
                </div>
              );
            },
            h1({ children }) {
              return <h1 className="my-0">{children}</h1>;
            },
            h2({ children }) {
              return <h2 className="my-0">{children}</h2>;
            },
            h3({ children }) {
              return <h3 className="my-0">{children}</h3>;
            },
            h4({ children }) {
              return <h4 className="my-0">{children}</h4>;
            }, 
            h5({ children }) {
              return <h5 className="my-0">{children}</h5>;
            }, 
            h6({ children }) {
              return <h6 className="my-0">{children}</h6>;
            }, 
            ol({ children }) {
              return <ol className="my-0">{children}</ol>;
            },
            ul({ children }) {
              return <ul className="my-0">{children}</ul>;
            },
            li({ children }) {
              return <li className="my-0">{children}</li>;
            },
            code(props) {
              const {children, className, node, ...rest} = props
              const match = /language-(\w+)/.exec(className || '')
              return match ? (
                <SyntaxHighlighter
                  {...rest}
                  ref={codeBlockRef}
                  PreTag="div"
                  children={String(children).replace(/\n$/, '')}
                  language={match[1]}
                  style={dark}
                />
              ) : (
                <code {...rest} className={className}>
                  {children}
                </code>
              );
            }
          }}
        >
          {content}
        </MemoizedReactMarkdown>
      </p>
      {!isAiMessage && user?.imageUrl && (
        <Image
          src={user.imageUrl}
          alt="User image"
          width={100}
          height={100}
          className="ml-2 h-10 w-10 rounded-full object-cover"
        />
      )}
      <ChatMessageActions message={message} path={path} />
    </div>
  );
}