'use client';

import React, { useState } from 'react'
import { DefaultChatTransport } from 'ai';
import { useChat } from '@ai-sdk/react';
import Image from 'next/image';
import Modal from '@/components/modals/Modal';
import { Button } from "@/components/ui/button";

interface ChatWithVisionModalProps {
  companionId: string
  src: string | null
  isOpen?: boolean;
  onClose: () => void;
}

const ChatWithVisionModal: React.FC<ChatWithVisionModalProps> = ({ 
  companionId, 
  src,
  isOpen, 
  onClose,
}) => {
  if (!src) {
    return null;
  }
  const [isLoading, setIsLoading] = useState(false);
  const [input, setInput] = useState('');
  const {
    messages,
    sendMessage,
    status
  } = useChat({
    transport: new DefaultChatTransport({
      api: '/api/chat-with-vision'
    })
  });

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form 
        onSubmit={e => {
          e.preventDefault();
          sendMessage(
            { role: 'user', parts: [{ type: 'text', text: input }] },
            { body: { imageUrl: src } }
          );
          setInput('');
        }}
      >
        <div className="space-y-2">
          <div className="border-b border-gray-900/10 pb-2">
            <h2 
              className="
                text-base 
                font-semibold 
                leading-7 
                text-gray-900
              "
              >
                圖畫討論
              </h2>
            <p className="mt-1 text-sm leading-6 text-gray-600">
              與AI討論這張圖
            </p>
            <div className="">
              <Image
                alt="Image"
                width="96"
                height="96"
                sizes="(max-width: 512px) 100vw,
                        (max-width:512px) 50vw,
                        33vw"
                src={src}
                className="
                  object-cover
                  cursor-pointer"
              />
            </div>
            <div className="mt-10 flex flex-col gap-y-8">
              <input
                className="
                  fixed
                  bottom-0
                  w-[80%]
                  max-w-md 
                  p-2 
                  mb-6 
                  border 
                  border-gray-300 
                  rounded 
                  shadow-xl"
                value={input}
                placeholder="What does the image show..."
                onChange={e => setInput(e.target.value)}
              />
            </div>
          </div>
        </div>
        <div className="mt-6 flex items-center justify-end gap-x-6">
          <Button disabled={isLoading} variant="premium">
            Submit
          </Button>
        </div>
      </form>
    </Modal>
  );
}

export default ChatWithVisionModal;