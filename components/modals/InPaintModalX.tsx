'use client'

import React, {
  FormEvent, useEffect, useRef,
  useState, useTransition, ChangeEvent } from 'react'
import Modal from '@/components/modals/Modal';
import DOMPurify from 'dompurify'
import fs from 'fs'
import ImageComponent from '@/components/ImageComponent'
import Textarea from 'react-textarea-autosize'
import MediaEditor from '@/components/gallery/MediaEditor'
import { cldUrlGenActions } from "@/app/actions/cldUrlGenActions"
import { cldUploadActions } from "@/app/actions/cldUploadActions"
import { nanoid } from '@/lib/utils'
import { CharacterType, MessageType } from '@/lib/CharacterType'
import { EmptyImageScreen } from '@/components/empty-image-screen'
import { parseAnswerToHtml } from '@/components/post/AnswerParser'
import { useUser } from "@clerk/nextjs"
import { useToast } from "@/components/ui/use-toast"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { useProModal } from "@/hooks/use-pro-modal"
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/callout'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { 
  Brush, Send, Eraser, ImagePlus, ImageOff, DownloadCloud,
  ChevronsLeft, UploadCloud, Heart, MessageCircle,
  RefreshCcw, RotateCw, RotateCcw, FlipVertical, Plus, Minus
} from "lucide-react";
import { Icon } from "@/components/Icon";
import { Slider } from '@/components/ui/slider'
import { BarLoader} from  'react-spinners'
import { cloudName } from "@/lib/cloudinaryConfig";
import { Cloudinary } from "@cloudinary/url-gen";
import { byAngle } from "@cloudinary/url-gen/actions/rotate";

interface InPaintProps {
  isOpen?: boolean;
  onClose: () => void;
  companionId: string
  image: string
  showAlert: (value: boolean, message: string) => void;
  selectedMediaUrl: string | null
}

interface ParsedData {
  data?: {
    url: string;
  }[];
  error?: {
    message: string;
  };
}

const InPaintModalX: React.FC<InPaintProps> = ({
  isOpen, 
  onClose,
  companionId,
  image,
  showAlert,
  selectedMediaUrl
}) => {
  const router = useRouter()
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const proModal = useProModal()
  const { user } = useUser()
  const userId = user?.id
  const { toast } = useToast()
  const imageUrlLink = searchParams?.get('imageUrlLink') || '';

  const count = 1;

  const alertMessage = "Check your description.";

  const size256 = "256x256";
  const size512 = "512x512";
  const size1024 = "1024x1024";

  const [size, setSize] = useState<string>(size512);
  const [cldUploadImage, setCldUploadImage] = useState<string | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedImageSize, setUploadedImageSize] = useState<string>(size512);
  const [ghostImage, setGhostImage] = useState<string | null>(null);
  const [uploadedImageFile, setUploadedImageFile] = useState<File | null>(null);
  const [uploadedImageBlob, setUploadedImageBlob] = useState<Blob | null>(null);
  const [uploadedImageLoading, setUploadedImageLoading] = useState<boolean>(false);
  const [loading, setIsLoading] = useState<boolean>(false)
  const [isPending, startTransition] = useTransition();
  const [text, setText] = useState<string>("");
  const [speakContent, setSpeakContent] = useState('')
  const [isCanvas, setIsCanvas] = useState<boolean>(true);
  const [brushSize, setBrushSize] = useState<number>(10);
  const [imageTransformMode, setImageTransformMode] = useState<string>('');
  const [rotateValue, setRotateValue] = useState(0);
  const [granularValue, setGranularValue] = useState(512);
  const [open, setOpen] = useState(false)
  const [imageAutoKeep, setImageAutoKeep] = useState<boolean>(false)
  const path = `/vision/${companionId}`


    /*
  imageCaption: While cldUploadPNG is true ,api: image2text //(HuggingFace)
  uploadPNG: Upload image to memory => setUploadedImage, setUploadedImageFile
  fetchPNGfromURL: Transform url to image blob => setUploadedImage, setUploadedImageBlob, setCldUploadImage api: imageUrl-stream
  cldUploadPNG(cldUploadImage): Upload image to cloudinary with tags and image caption => setUploadedImage, setUploadedImageBlob, call HF imageCaption() api: cldUpload ////收藏圖片
  fetchUrlLinkImage: Get imageUrlLink from searchParams => call fetchPNGfromURL()
  selectedMediaUrlClick(selectedMediaUrl): Fetch selectedMediaUrl from Gallery(), => call fetchPNGfromURL()
  cldImageTransform: Apply image transformtions via @cloudinary/url-gen => setUploadedImage, setCldUploadImage, api: cldUrlGen
  handleMediaEditorExport(secureUrl): Fetch MediaEditor export Url, then fetchPNGfromURL()
  generateImage: => setCldUploadImage...
  */

  useEffect(() => {
    fetchUrlLinkImage();
  }, []);

  const fetchUrlLinkImage = async () => {
    if (userId && imageUrlLink) {
      const imageUrl = Array.isArray(imageUrlLink) ? imageUrlLink.join('') : imageUrlLink;
      setCldUploadImage(imageUrl);
      await fetchPNGfromURL(imageUrl);
      setIsCanvas(true);
      setIsLoading(false)
      clearMaskedAreas();
      //router.refresh()
    }
  };

  useEffect(() => {
    if (image !== null && image !== undefined && image.length > 0) {
      const fetchImage = async () => {
        try {
          console.log("image: ", image);
          setCldUploadImage(image);
          await fetchPNGfromURL(image);
          setIsCanvas(true);
          clearMaskedAreas();
        } catch (error) {
          console.error("Error fetching image:", error);
        }
      };

      fetchImage();
    }
  }, [image]); 

  const changeDisabilityOfAllButtons = (value: boolean) => {
    const buttons = ["size1", "size2", "size3", "generate", "uploadpng", "clearmask", "cldupload"];
    buttons.forEach((buttonId) => {
      const button = document.getElementById(buttonId) as HTMLButtonElement | HTMLInputElement | null;
      if (button) {
        button.disabled = value;
      }
    });

  // Styling of upload custom button
  const uploadPngLabel = document.getElementById("uploadpnglabel") as HTMLLabelElement | null;
    if (uploadPngLabel) {
      uploadPngLabel.style.backgroundColor = value ? "#6f7174" : "#212529";
      uploadPngLabel.style.cursor = value ? "default" : "pointer";
    }
  };

  const setSizeAndButtons = (size: string) => {
    setSize(size);
    setCorrespondingSizeButtonDisabled(size);
  };

  const setCorrespondingSizeButtonDisabled = (size: string) => {
    const size1Button = document.getElementById("size1") as HTMLButtonElement | null;
    const size2Button = document.getElementById("size2") as HTMLButtonElement | null;
    const size3Button = document.getElementById("size3") as HTMLButtonElement | null;

    if (size1Button && size2Button && size3Button) {
      size1Button.disabled = size === size256;
      size2Button.disabled = size === size512;
      size3Button.disabled = size === size1024;
    }
  };

  
  // Press Enter to submbit、Shift+Enter to next line
  const enterPress: React.KeyboardEventHandler<HTMLTextAreaElement> =
   (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key == 'Enter' && e.shiftKey == false) {
      e.preventDefault()
      generateImage(count, size, text)
    }
  }


  const selectedMediaUrlClick = async (url: string) => {
    try {
      //console.log("fetchPNGfromURL", url);
      await fetchPNGfromURL(url);
      setIsCanvas(true);
      router.refresh();
    } catch (error: any) {
      toast({
        variant: "destructive",
        description: "Something went wrong.",
        duration: 3000,
      });
      console.error("Error:", error);
      setIsLoading(false)
    }
  };


  // Roteate image from cloudinary via server-mode api
  useEffect(() => {
    if (cldUploadImage || uploadedImage) {
      startTransition(async () => {
        let extractedImageUrl: string = '' 
        const match = image?.match(/\/([^/]+)\/([^/?#]+\.png)/);
        if (match) {
          const folderName = match[1]; // This will be "images"
          const fileName = match[2];   // This will be "out-2_zwmma7.png"
          extractedImageUrl = `${folderName}/${fileName}`;
          
          //console.log("Folder Name:", folderName);
          //console.log("File Name:", fileName);
          //console.log("extractedImageUrl: ", extractedImageUrl)
        } else {
          console.log("URL doesn't match the expected pattern.");
        }        
        try {
          const transformImgURL = await cldUrlGenActions({
            imageTransformMode,
            imageUrl: extractedImageUrl,
            granularValue,
            rotateValue,
            path
          })
    
          if (typeof transformImgURL === 'string') {
            setUploadedImage(transformImgURL);
            setCldUploadImage(transformImgURL);
          } else if (transformImgURL && 'error' in transformImgURL) {
            console.error(transformImgURL.error);
          }
          
        } catch (error) {
          console.error('An unexpected error occurred:', error);
        }
      })
      console.log("rotateValue: ", rotateValue)
    }
  }, [granularValue, rotateValue]);


  useEffect(() => {
    console.log("setUploadedImage:", uploadedImage);
    console.log("cldUploadImage:", cldUploadImage);
    console.log("granularValue:", granularValue);
    console.log("imageTransformMode:", imageTransformMode);
  }, [uploadedImage, cldUploadImage, granularValue, imageTransformMode]);

  
  const cldUploadPNG = async () => {
    setIsLoading(true)
    // Upload the image to Cloudinary
    const cloudinaryResponse = await fetch("/api/cldUpload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ 
        companionId: companionId, 
        imageUrl: cldUploadImage }),
    });

    if (!cloudinaryResponse.ok) {
      // Handle Cloudinary upload errors
      setIsLoading(false)
      showAlert(true, "Failed to upload the image from the URL.");
      return;
    }

    interface CloudinaryResponse {
    result: {
        tags: []
        url: string
        secure_url: string
        info: {
          detection: {
            captioning: {
              data: {
                caption: string;
              };
            };
          };
        };
      };
    }

    const cloudinaryData: CloudinaryResponse = await cloudinaryResponse.json();
      const imageTags: [] = cloudinaryData.result.tags
      const imageCaptionContent: string = cloudinaryData.result.info.detection.captioning.data.caption
      const imageTagListDiv = document.getElementById("imageTagList");
      // Update aiMessage
      /*const aiMessage: Message = {
        id: companion.id,
        role: "assistant",
        content: '這張圖裡面包含：' + imageCaptionContent,
      };
      setMessages([...messages, aiMessage])*/
      function imageTagOnClick(event: Event) {
        const li = event.currentTarget as HTMLLIElement;
        const clickedImageTag = li.textContent;
        setText(clickedImageTag ?? '')
        //alert(`You clicked on tag: ${clickedImageTag}`);
        router.refresh()
      }

      if (imageTagListDiv) {
        const ul = document.createElement("ul");
        ul.style.listStyleType = "none";
      
        imageTags.forEach((tag) => {
          const li = document.createElement("li");
          li.style.display = "inline";
          li.style.marginRight = "10px";
          li.style.color = "black";
          li.style.padding = "2px";
          li.style.paddingLeft = "8px";
          li.style.paddingRight = "8px";
          li.style.backgroundColor = "burlywood";
          li.style.borderRadius = "5px"
          li.style.cursor = "pointer";
          const span = document.createElement("span");
          span.textContent = tag;
          span.style.backgroundColor = "burlywood";
          span.style.borderRadius = "5px"
          li.addEventListener("click", imageTagOnClick);
          li.appendChild(span);
          ul.appendChild(li);
        });
      
        imageTagListDiv.appendChild(ul);
      }
      
    console.log("Cloudinary response:", cloudinaryData);
    setIsLoading(false)
    router.refresh()
  }


  const fetchPNGfromURL = async (imageUrl: string) => {
    setUploadedImageLoading(true);
    try {
      /*
      // Fetch the image data from the URL
      const response = await fetch(imageUrl);
      */

      // Make a request to your custom API endpoint
      const response = await fetch('/api/imageUrl-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      });

      if (!response.ok) {
        // Handle the case where the image could not be fetched
        showAlert(true, "Failed to fetch the image from the URL.");
        return;
      }
      
      // Get the blob data for the image
      const imageBlob = await response.blob();

      console.log("imageBlob: ", imageBlob)

      // Create a Data URL for the image
      const imageUrlData = URL.createObjectURL(imageBlob);

      // Update the state to display the image
      setUploadedImage(imageUrlData);
      setUploadedImageBlob(imageBlob);
      setGhostImage(imageUrlData);
      //(document.getElementById("descText") as HTMLTextAreaElement).value = "";
      setUploadedImageFile(null);
      router.refresh()
    } catch (error) {
      console.error("Error fetching image from URL:", error);
      showAlert(true, "An error occurred while fetching the image from URL.");
    } finally {
      setUploadedImageLoading(false);
    }
  };

  const cleanPNG = async (clearMaskedAreas: () => void) => {
    setIsLoading(true);
    setUploadedImage(null);
    setCldUploadImage(null);
    setUploadedImageBlob(null);
    setUploadedImageFile(null);
    setGhostImage(null);
    setIsLoading(false);
  
    if (typeof clearMaskedAreas === 'function') {
      clearMaskedAreas();
      setUploadedImage(null);
    }
  };

  const clearMaskedAreas = () => {
    if (uploadedImageFile !== null) {
      setUploadedImage(URL.createObjectURL(uploadedImageFile));
    } else if (uploadedImageBlob !== null) {
      console.log("clearMaskArea for ImageBlob: ", uploadedImageBlob);
      setUploadedImage(URL.createObjectURL(uploadedImageBlob));
    }
    const sizePlus = firstSize.current - 1;
    setUploadedImageSize(`${sizePlus}x${sizePlus}`);
  };
  
  const uploadPNG = (e: ChangeEvent<HTMLInputElement>) => {    
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.type !== "image/png") {
        showAlert(true, "File you uploaded is not in a valid format. Upload an image/png file.");
      } else if (file.size >= 4000000) {
        showAlert(true, "Upload an image/png file less than 4MB.");
      } else {        
        const img = new Image();
        img.src = URL.createObjectURL(file);
        img.onload = () => {
          if (img.width !== img.height) {
            showAlert(true, "Image you uploaded is not a square. Upload a square image.");
          } else {            
            setUploadedImage(URL.createObjectURL(file));
            setUploadedImageSize(`${img.width}x${img.height}`);
            firstSize.current = img.width;
            setUploadedImageFile(file);
            setGhostImage(URL.createObjectURL(file));
            //(document.getElementById("descText") as HTMLTextAreaElement).value = "";
            setUploadedImageBlob(null);
            router.refresh()
          }
        }
      }
    }
  }

  const firstSize = useRef<number>(512);

  useEffect(() => {
    if (uploadedImageSize === "255x255" || uploadedImageSize === "511x511" || uploadedImageSize === "1023x1023") {
      setUploadedImageSize(`${firstSize.current}x${firstSize.current}`);
    }
  }, [uploadedImageSize]);

  useEffect(() => {
    const generateButton = document.getElementById("generate") as HTMLButtonElement | null;
    if (generateButton) {
      //generateButton.disabled = uploadedImage === null;
      generateButton.disabled = text === "";
    }
  }, [uploadedImage, text]);
  

  useEffect(() => {
    const size1Button = document.getElementById("size1") as HTMLButtonElement | null;
    if (size1Button) {
      size1Button.disabled = true;
    }
  }, []);
  

  const generateImage = async (count: number, size: string, text: string) => {
    const myCanvas = document.getElementById("myCanvas") as HTMLCanvasElement | null;
    let parsedData: ParsedData = {};
    let formData: FormData | null = null;

    if (myCanvas) {
      const dataURL = myCanvas.toDataURL();
      console.log("dataURL: ", dataURL)
      const response = await fetch(dataURL);
      console.log("response: ", response)
      const data = await response.blob();
      console.log("data: ", data)
      const metadata = {
        type: 'image/png'
      };

      // Image continuing edit
      if (
        count >= 1 &&
        (size === size256 || size === size512 || size === size1024) &&
          uploadedImageBlob !== null && uploadedImageFile === null &&
        text !== "" 
      ) {
        console.log("Debugging: count =", count);
        console.log("Debugging: size =", size);
        console.log("Debugging: uploadedImageBlob =", uploadedImageBlob);
        console.log("Debugging: text =", text);

        let maskFile = new File([data], "contEditMask.png", metadata);
        if (maskFile === null) {
          showAlert(true, "An error occurred during the transformation of the mask image. Try again later.");
        } else {
          setUploadedImageLoading(true);
          setUploadedImage(null);
          setIsCanvas(false);
          changeDisabilityOfAllButtons(true);
          setUploadedImageSize(size);

          formData = new FormData();
          formData.append('mode', "contEdit");
          formData.append('image', uploadedImageBlob);
          formData.append('size', size);
          formData.append('n', count.toString());
          formData.append('prompt', text);
          formData.append('mask', maskFile);
          console.log("Image upload: ", formData);
        }

      // Image first time edit
      } else if (
        count >= 1 &&
        (size === size256 || size === size512 || size === size1024) &&
          uploadedImageFile !== null && uploadedImageBlob === null &&
        text !== "" 
      ) {
        console.log("Debugging: count =", count);
        console.log("Debugging: size =", size);
        console.log("Debugging: uploadedImageFile =", uploadedImageFile);
        console.log("Debugging: text =", text);

        let maskFile = new File([data], "editMask.png", metadata);
        if (maskFile === null) {
          showAlert(true, "An error occurred during the transformation of the mask image. Try again later.");
        } else {
          setUploadedImageLoading(true);
          setUploadedImage(null);
          setIsCanvas(false);
          changeDisabilityOfAllButtons(true);
          setUploadedImageSize(size);

          formData = new FormData();
          formData.append('mode', "edit");
          formData.append('image', uploadedImageFile);
          formData.append('size', size);
          formData.append('n', count.toString());
          formData.append('prompt', text);
          formData.append('mask', maskFile);
          console.log("Image upload: ", formData);
        }

      // Image generation
      } else if (
        count >= 1 &&
        (size === size256 || size === size512 || size === size1024) &&
         text !== ""
      ) {
        setUploadedImageLoading(true);
        setUploadedImage(null);
        setIsCanvas(false);
        changeDisabilityOfAllButtons(true);
        formData = new FormData();
        formData.append('mode', "generate");
        formData.append('size', size);
        formData.append('n', count.toString());
        formData.append('prompt', text);

        console.log("Prompt only: ", formData);

      } else {
        showAlert(true, alertMessage);
      }
    }

    try {
      if (formData !== null) {
        const requestOptions = {
          method: 'POST',
          body: formData,
        };
        const response = await fetch('/api/image/dalle', requestOptions);

        /*if (!response.ok) {
          throw new Error('Request failed with status ' + response.status);
        }*/
        parsedData = await response.json();
        console.log("parsedData", parsedData);
      }
    } catch (error: any) {
      if (error?.response?.status === 403) {
        proModal.onOpen();
      } else if (error?.response?.status === 400) {
        const errorMessage = error?.response?.data?.message || "Bad Request";
        toast({
          variant: "destructive",
          description: errorMessage,
          duration: 3000,
        });
      } else {
        toast({
          variant: "destructive",
          description: "Something went wrong.",
          duration: 3000,
        });
      }   
    }

    if (
      Array.isArray(parsedData) &&
      parsedData.length > 0 &&
      parsedData[0].url !== undefined &&
      parsedData[0].url !== "" &&
      parsedData[0].url !== null
    ) {
      let imageUrl = parsedData[0].url;
      console.log("imageUrl: ", imageUrl)
      setCldUploadImage(imageUrl);
      await fetchPNGfromURL(imageUrl);
      setIsCanvas(true);
      changeDisabilityOfAllButtons(false);
      setCorrespondingSizeButtonDisabled(size);
      setUploadedImageLoading(false);
  
            
    } else if (parsedData.error !== null && parsedData.error !== undefined) {
      console.log(parsedData.error.message);
      showAlert(true, parsedData.error.message);
      setUploadedImageLoading(false);
      setIsCanvas(true);
      changeDisabilityOfAllButtons(false);
      setCorrespondingSizeButtonDisabled(size);
    
    }
    setText('')
  };


  const brushSizeChanged = (value: number[]) => {
    const numValue = value[0]
    setBrushSize(numValue)
  }

  function setIncreaseValue(e: React.MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
    // set allowable samllest image size, smilar to zoom-in image size from 200 to fit 512 canvas box
    if ( imageTransformMode === "resizeCrop" && granularValue >= 200) {
      setGranularValue(() => granularValue - 100);
    } else if ( imageTransformMode === "rotate") {
      setRotateValue(() => rotateValue + 10);      
    }
  }

  function setDecreaseValue(e: React.MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    // set allowable samllest image size,
    if ( imageTransformMode === "resizeCrop" && granularValue < 512) {
      // zoom-in image
      setGranularValue(() => granularValue + 100);
    } else if ( imageTransformMode === "resizeCrop" && granularValue >= 512) {  
      //zoom-out image
      setImageTransformMode("resizeScale")
      setGranularValue(() => granularValue + 1);
    } else if ( imageTransformMode === "rotate") {
      setRotateValue(() => rotateValue + 10);      
    }
    // use useEffect to call cldImageTransform
  }

  function setCWRotation(e: React.MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
    setImageTransformMode("rotate")
    setRotateValue(() => rotateValue + 10);
    // use useEffect to call cldImageTransform() or rotateImages()
  }

  function setCCWRotation(e: React.MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    setImageTransformMode("rotate")
    setRotateValue(() => rotateValue - 10);
    // use useEffect to call cldImageTransform or rotateImages()
  }

  function setFlipVertical(e: React.MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    setRotateValue(() => rotateValue + 90);
    // use useEffect to call cldImageTransform or rotateImages()
  }

  const handleMediaEditorExport = async (secureUrl: string) => {
    setIsLoading(true)
    await fetchPNGfromURL(secureUrl);
    setIsCanvas(true);
    console.log("Received Secure URL: ", secureUrl);
    setIsLoading(false)
  };

  const handleImageDownload = (imageUrl: string) => {
    if (imageUrl) {
      //const filename = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
      const filename = `${nanoid()}${nanoid()}.png`
      fetch(imageUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
    }
  };
  

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div>
        <div className={cn('grid grid-cols-1 gap-0 overflow-hidden')}>
          <div className='flex items-center justify-center col-span-1'>
            {/* Content on the Left side */}
            <div id="imageTagList" className='fixed top-1 left-[20%] h-12 w-300 flex items-center justify-center p-2'></div>
            {ghostImage !== null && (
              <img src={ghostImage} alt="..." id="ghostimage" hidden />
            )}
            <ImageComponent
              image={uploadedImage}
              size={uploadedImageSize}
              loading={uploadedImageLoading}
              isCanvas={isCanvas}
              brushSize={brushSize}
            />          
            <div className='absolute bottom-1 flex flex-row justify-center items-center'>
              <div className='mt-1'>
                <MediaEditor onExport={handleMediaEditorExport} />
              </div>
              <div className='mt-1'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <label
                      id="uploadpnglabel"
                      className="inline-block cursor-pointer px-2 py-1"
                      style={{
                        backgroundColor: "transparent",
                        borderRadius: "4px",
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      <ImagePlus color="cyan" className="w-5 h-5" />
                      <input type="file"
                        className="hidden"
                        id="uploadpng"
                        onChange={uploadPNG}
                        disabled={uploadedImageLoading || loading}
                      />
                    </label>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Upload Image (size must be 512x512)</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-1'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent px-2 py-1" 
                      id="clearmask" 
                      type="button" 
                      onClick={clearMaskedAreas}
                      disabled={uploadedImageLoading || loading}
                    >
                      <RefreshCcw color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clear Mask</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-1 w-[5rem] flex items-center cursor-pointer'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Slider
                      min={1}
                      max={30}
                      defaultValue={[brushSize]}
                      id="slider"
                      onValueChange={(value) => brushSizeChanged(value)}
                      className="h-[90%]"
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Adjust Brush Size</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-1'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent px-2 py-1" 
                      id="cleanpng" 
                      type="button" 
                      onClick={() => cleanPNG(clearMaskedAreas)}
                      disabled={uploadedImageLoading || loading}
                    >
                      <ImageOff color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>刪除底圖</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-1'>
                <Select                
                  disabled={uploadedImageLoading || loading}
                  onValueChange={(value) => setImageTransformMode(value)}
                >
                  <SelectTrigger className="w-[6rem] px-1">
                    <SelectValue placeholder="調整圖像" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="resizeCrop">場景遠近</SelectItem>
                    <SelectItem value="rotate">旋轉圖像</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='ml-2 mt-1 w-[3rem]'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Switch
                      className='flex items-center cursor-pointer bg-primary/10 space-x-2'
                      id="imageAutoKeep"
                      checked={imageAutoKeep}
                      onCheckedChange={(e) => setImageAutoKeep(e)}
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>自動收藏圖片</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            <div className='mt-1 mr-[1.1rem]'>
              <div className='mt-2'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1"
                      onClick={setFlipVertical}
                      disabled={!uploadedImage || !cldUploadImage}
                    >
                      <FlipVertical color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>垂直旋轉</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-2'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1"
                      onClick={setCCWRotation}
                      disabled={!uploadedImage || !cldUploadImage}
                    >
                      <RotateCcw color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>向左旋轉</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-2'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1"
                      onClick={setCWRotation}
                      disabled={!uploadedImage || !cldUploadImage}
                    >
                      <RotateCw color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>向右旋轉</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-2'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1"
                      onClick={setIncreaseValue}
                      disabled={!uploadedImage || !cldUploadImage}
                    >
                      <Plus color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>增加</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-2'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1"
                      onClick={setDecreaseValue}
                      disabled={!uploadedImage || !cldUploadImage}
                    >
                      <Minus color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>減少</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-5'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1" 
                      id="cldupload" 
                      type="button" 
                      onClick={() => cldUploadPNG()}
                      disabled={!cldUploadImage || uploadedImageLoading || loading}
                    >
                      <Heart color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>收藏這張圖片</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className='mt-2'>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="flex items-center cursor-pointer bg-transparent p-1"
                      disabled={!uploadedImage || !cldUploadImage}
                      onClick={() => handleImageDownload(cldUploadImage as string)}
                    >
                      <DownloadCloud color="cyan" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>下載</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
      {uploadedImage || cldUploadImage ? (
        <div className="flex justify-center items-center z-20">
          <BarLoader color={'#36d7b7'}  />
        </div>
      ) : null}
    </Modal>
  ); 
};

export default InPaintModalX