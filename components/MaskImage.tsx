'use client'

import React, { 
  FormEvent, 
  useEffect, 
  useRef, 
  useState, 
  ChangeEvent } from 'react'
import axios from 'axios'
import DOMPurify from 'dompurify'
import ImageComponent from './ImageComponent'
import Textarea from 'react-textarea-autosize'
import MediaEditor from '@/components/gallery/MediaEditor'
import { DefaultChatTransport } from "ai";
import { useChat, type UIMessage } from '@ai-sdk/react';
import { ChatForm } from "@/components/chat-form"
import { TalkMessages } from "@/components/talk-messages"
import { TalkMessageProps } from "@/components/talk-message"
import { Companion } from "@prisma/client";
import { CharacterType, MessageType } from '@/lib/CharacterType'
import { EmptyImageScreen } from '@/components/empty-image-screen'
import { parseAnswerToHtml } from '@/components/post/AnswerParser'
import { useUser } from "@clerk/nextjs"
import { useToast } from "@/components/ui/use-toast"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { useProModal } from "@/hooks/use-pro-modal"
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/callout'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { 
  Brush, Send, Eraser, ImagePlus, ImageOff, 
  ChevronsLeft, UploadCloud, Heart, MessageCircle,
  RefreshCcw, RotateCw, RotateCcw, FlipVertical, Plus, Minus
} from "lucide-react";
import { Icon } from "@/components/Icon";
import { Slider } from '@/components/ui/slider'
import { BarLoader} from  'react-spinners'
import { cloudName } from "@/lib/cloudinaryConfig";
import { Cloudinary } from "@cloudinary/url-gen";
import { byAngle } from "@cloudinary/url-gen/actions/rotate";

interface MaskImageProps {
  companion: Companion & {
    messages: UIMessage[]
  },
  showAlert: (value: boolean, message: string) => void;
  selectedMediaUrl: string | null
}

interface ParsedData {
  data?: {
    url: string;
  }[];
  error?: {
    message: string;
  };
}

const MaskImage: React.FC<MaskImageProps> = (props) => {
  const router = useRouter()
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const proModal = useProModal()
  const { user } = useUser()
  const userId = user?.id
  const { toast } = useToast()
  const { companion, showAlert, selectedMediaUrl } = props
  const imageUrlLink = searchParams?.get('imageUrlLink') || '';

  const count = 1;

  const alertMessage = "Check your description.";

  const size256 = "256x256";
  const size512 = "512x512";
  const size1024 = "1024x1024";

  const [size, setSize] = useState<string>(size512);
  const [cldUploadImage, setCldUploadImage] = useState<string | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedImageSize, setUploadedImageSize] = useState<string>(size512);
  const [ghostImage, setGhostImage] = useState<string | null>(null);
  const [uploadedImageFile, setUploadedImageFile] = useState<File | null>(null);
  const [uploadedImageBlob, setUploadedImageBlob] = useState<Blob | null>(null);
  const [uploadedImageLoading, setUploadedImageLoading] = useState<boolean>(false);
  const [loading, setIsLoading] = useState<boolean>(false)
  const [text, setText] = useState<string>("");
  const [speakContent, setSpeakContent] = useState('')
  const [isCanvas, setIsCanvas] = useState<boolean>(true);
  const [brushSize, setBrushSize] = useState<number>(10);
  const [imageTransformMode, setImageTransformMode] = useState<string>('');
  const [rotateValue, setRotateValue] = useState(0);
  const [granularValue, setGranularValue] = useState(512);
  const [chatHistoryMessages, setChatHistoryMessages] = useState<MessageType[]>([])
  const [open, setOpen] = useState(false)
  const [imageAutoKeep, setImageAutoKeep] = useState<boolean>(false)
  const messageHandler = (chatHistoryMessage: MessageType) => {
    setChatHistoryMessages((chatHistoryMessages) => [...chatHistoryMessages, chatHistoryMessage])
  }

  /*
imageCaption: While cldUploadPNG is true ,api: image2text //(HuggingFace)
uploadPNG: Upload image to memory => setUploadedImage, setUploadedImageFile
fetchPNGfromURL: Transform url to image blob => setUploadedImage, setUploadedImageBlob, setCldUploadImage api: imageUrl-stream
cldUploadPNG(cldUploadImage): Upload image to cloudinary with tags and image caption => setUploadedImage, setUploadedImageBlob, call HF imageCaption() api: cldUpload ////收藏圖片
fetchUrlLinkImage: Get imageUrlLink from searchParams => call fetchPNGfromURL()
selectedMediaUrlClick(selectedMediaUrl): Fetch selectedMediaUrl from Gallery(), => call fetchPNGfromURL()
cldImageTransform: Apply image transformtions via @cloudinary/url-gen => setUploadedImage, setCldUploadImage, api: cldUrlGen
handleMediaEditorExport(secureUrl): Fetch MediaEditor export Url, then fetchPNGfromURL()
generateImage: => setCldUploadImage...
*/

  useEffect(() => {
    fetchUrlLinkImage();
  }, []);

  const fetchUrlLinkImage = async () => {
    if (userId && imageUrlLink) {
      const imageUrl = Array.isArray(imageUrlLink) ? imageUrlLink.join('') : imageUrlLink;
      setCldUploadImage(imageUrl);
      await fetchPNGfromURL(imageUrl);
      setIsCanvas(true);
      setIsLoading(false)
      clearMaskedAreas();
      //router.refresh()
    }
  };

  const changeDisabilityOfAllButtons = (value: boolean) => {
    const buttons = ["size1", "size2", "size3", "generate", "uploadpng", "clearmask", "cldupload"];
    buttons.forEach((buttonId) => {
      const button = document.getElementById(buttonId) as HTMLButtonElement | HTMLInputElement | null;
      if (button) {
        button.disabled = value;
      }
    });

  // Styling of upload custom button
  const uploadPngLabel = document.getElementById("uploadpnglabel") as HTMLLabelElement | null;
    if (uploadPngLabel) {
      uploadPngLabel.style.backgroundColor = value ? "#6f7174" : "#212529";
      uploadPngLabel.style.cursor = value ? "default" : "pointer";
    }
  };

  const setSizeAndButtons = (size: string) => {
    setSize(size);
    setCorrespondingSizeButtonDisabled(size);
  };

  const setCorrespondingSizeButtonDisabled = (size: string) => {
    const size1Button = document.getElementById("size1") as HTMLButtonElement | null;
    const size2Button = document.getElementById("size2") as HTMLButtonElement | null;
    const size3Button = document.getElementById("size3") as HTMLButtonElement | null;

    if (size1Button && size2Button && size3Button) {
      size1Button.disabled = size === size256;
      size2Button.disabled = size === size512;
      size3Button.disabled = size === size1024;
    }
  };


  // Press Enter to submbit、Shift+Enter to next line
  const enterPress: React.KeyboardEventHandler<HTMLTextAreaElement> =
   (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key == 'Enter' && e.shiftKey == false) {
      e.preventDefault()
      generateImage(count, size, text)
    }
  }


  const selectedMediaUrlClick = async (url: string) => {
    try {
      //console.log("fetchPNGfromURL", url);
      await fetchPNGfromURL(url);
      setIsCanvas(true);
      router.refresh();
    } catch (error: any) {
      toast({
        variant: "destructive",
        description: "Something went wrong.",
        duration: 3000,
      });
      console.error("Error:", error);
      setIsLoading(false)
    }
  };


  const textChanged = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setText(event.target.value);
  };

  const [input, setInput] = useState('');

  const {
    messages,
    setMessages,
    sendMessage,
    status,
  } = useChat({
    messages: companion.messages,
    transport: new DefaultChatTransport({
      api: `/api/image/prompt/${companion.id}`,
      body: () => ({        
        companionId: companion.id,
        companionNickName: companion.name,
        companionInstructions: companion.instructions,
        companionSeed: companion.seed,
      }),
    }),


    onFinish: async (messages) => {
      setInput("");
      router.refresh();
    },


  });

  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage({ text: input} );
    setInput("");
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const isLoading = status !== 'ready' && status !== 'error';


  useEffect(() => {
    cldImageTransform();
  }, [granularValue]);

  useEffect(() => {
    cldImageTransform();
  }, [rotateValue]);


  const rotateCldImage = async () => {
    if (cldUploadImage || uploadedImage) {
      setIsLoading(true)
      const cld = new Cloudinary({
        cloud: {
          cloudName: cloudName,
        },
        url: {
            secure: true
        }
      });
      const rotateImg = await cld
        .image("samples/outdoor-woman.jpg")
        .rotate(byAngle(rotateValue));
      console.log("rotateImg: ", rotateImg)
      setUploadedImage(rotateImg.toURL());
      setCldUploadImage(rotateImg.toURL());
      setIsLoading(false)
    }
  };

  // Roteate image from cloudinary via server-mode api
  const cldImageTransform = async () => {
    let extractedImageUrl: string = '' 
    if (cldUploadImage || uploadedImage) {
      setIsLoading(true)
      const match = cldUploadImage?.match(/\/([^/]+)\/([^/?#]+\.png)/);

      if (match) {
        const folderName = match[1]; // This will be "v1696580405"
        const fileName = match[2];   // This will be "out-2_zwmma7.png"
        extractedImageUrl = `${fileName}`;
        
        //console.log("Folder Name:", folderName);
        //console.log("File Name:", fileName);
        //console.log("extractedImageUrl: ", extractedImageUrl)
      } else {
        console.log("URL doesn't match the expected pattern.");
      }
 
      const cloudinaryResponse = await fetch("/api/cldUrlGen", {
      //const cloudinaryResponse = await fetch("/api/cldImage", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          companionId: companion.id,
          imageTransformMode: imageTransformMode,
          imageUrl: extractedImageUrl, //"out-2_zwmma7.png",
          //imageUrl: cldUploadImage,
          granularValue: granularValue,
          rotateValue: rotateValue
        }),
      });

      if (!cloudinaryResponse.ok) {
        // Handle Cloudinary upload errors
        setIsLoading(false)
        props.showAlert(true, "Failed to upload the image from the URL.");
        return;
      }

      interface CloudinaryData {
        transformImgURL: string;
      }
      const cloudinaryData: CloudinaryData = await cloudinaryResponse.json()
      console.log("cloudinaryTransformResponse", cloudinaryData.transformImgURL)
      setUploadedImage(cloudinaryData.transformImgURL)
      setCldUploadImage(cloudinaryData.transformImgURL);
      setIsLoading(false)
    }
  }

  useEffect(() => {
    console.log("setUploadedImage:", uploadedImage);
    console.log("cldUploadImage:", cldUploadImage);
    console.log("granularValue:", granularValue);
    console.log("imageTransformMode:", imageTransformMode);
  }, [uploadedImage, cldUploadImage, granularValue, imageTransformMode]);

  useEffect(() => {
    const ImageCaption = async () => {
      if (cldUploadImage && text) {
        setIsLoading(true);
        try {
          const body = JSON.stringify({ uploadedImageUrl: cldUploadImage });
          const response = await fetch(`/api/image2text`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body,
          })

          const response_data = await response.json();
          const response_data_text = '這張圖裡面包含：' + response_data.text //HF
          console.log("response_data from image2text: ", response_data_text)


          // Update aiMessage
          let aiMessage: UIMessage = {
            id: companion.id,
            role: "assistant",
            parts: [{ type: 'text', text: response_data_text }],
          };
    
          setMessages([...messages, aiMessage]);

          let sanitizedAnswer
          const promptIntercept: boolean = false
          if (promptIntercept) {
            await axios.post(
              "/api/talk",
              {
                companionId: companion.id,
                content: response_data_text,
                role: "assistant",
              }
            );

            const callToActionTemplate: string =
            `You are a dalle image prompt generator,` +
            ` Your goal is to translate user input to an image prompt` +
            `\nWhenever response, only use zh-tw` +
            `\nHere is user input: ${response_data_text}` +
            `\n\nImage prompt: `;

            // Chain text2image respond to llm
            const question = response_data_text
            const messageQuestion = { type: 'question', text: question }
            messageHandler(messageQuestion)

            // Ask chatgpt questions and get answers
            const responseChatGPT = await axios.post(`/api/chatgpt`, {
              promptTemplate: callToActionTemplate,
              maxOutputTokens: 500,
              temperature: 0.8,
              question,
              chatHistoryMessages,
            })

            const answer = responseChatGPT?.data?.response

            if (answer) {
              sanitizedAnswer = DOMPurify.sanitize(parseAnswerToHtml(answer).answerHtml)
              setSpeakContent(sanitizedAnswer)            

              // Add to message list
              const messageAnswer = { type: 'answer', text: sanitizedAnswer }
              messageHandler(messageAnswer)

              // Update the content of the existing aiMessage
              aiMessage = {
                ...aiMessage,
                parts: [{ type: 'text', text: sanitizedAnswer }],
              };
        
              setMessages([...messages, aiMessage])
            }

          } else {
            sanitizedAnswer = response_data_text          
          }
          setIsLoading(false)

          if (imageAutoKeep) {
            cldUploadPNG() // ***Upload the Image to Cloudinary***
          }
          
          if (sanitizedAnswer) {
            // Insert data to database
            await axios.post(
              "/api/talk",
              {
                companionId: companion.id,
                content: text,
                role: "user",
              }
            );

            await axios.post(
              "/api/talk",
              {
                companionId: companion.id,
                content: sanitizedAnswer,
                role: "assistant",
              }
            );
          }

        } catch (error: any) {
          if (error?.response?.status === 403) {
            proModal.onOpen();
          } else {
            toast({
              variant: "destructive",
              description: "Something went wrong.",
              duration: 3000,
            });
            console.error(error)
          }
        } finally {
          setIsLoading(false)
          router.refresh()
        }
      }
    }

    ImageCaption()
  }, [cldUploadImage])


  const cldUploadPNG = async () => {
    setIsLoading(true)
    // Upload the image to Cloudinary
    const cloudinaryResponse = await fetch("/api/cldUpload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ 
        companionId: companion.id, 
        imageUrl: cldUploadImage }),
    });

    if (!cloudinaryResponse.ok) {
      // Handle Cloudinary upload errors
      setIsLoading(false)
      props.showAlert(true, "Failed to upload the image from the URL.");
      return;
    }

    interface CloudinaryResponse {
    result: {
        tags: []
        url: string
        secure_url: string
        info: {
          detection: {
            captioning: {
              data: {
                caption: string;
              };
            };
          };
        };
      };
    }

    const cloudinaryData: CloudinaryResponse = await cloudinaryResponse.json();
      const imageTags: [] = cloudinaryData.result.tags
      const imageCaptionContent: string = cloudinaryData.result.info.detection.captioning.data.caption
      const imageTagListDiv = document.getElementById("imageTagList");
      // Update aiMessage
      /*const aiMessage: Message = {
        id: companion.id,
        role: "assistant",
        content: '這張圖裡面包含：' + imageCaptionContent,
      };
      setMessages([...messages, aiMessage])*/
      function imageTagOnClick(event: Event) {
        const li = event.currentTarget as HTMLLIElement;
        const clickedImageTag = li.textContent;
        setText(clickedImageTag ?? '')
        //alert(`You clicked on tag: ${clickedImageTag}`);
        router.refresh()
      }

      if (imageTagListDiv) {
        const ul = document.createElement("ul");
        ul.style.listStyleType = "none";
      
        imageTags.forEach((tag) => {
          const li = document.createElement("li");
          li.style.display = "inline";
          li.style.marginRight = "10px";
          li.style.color = "black";
          li.style.padding = "2px";
          li.style.paddingLeft = "8px";
          li.style.paddingRight = "8px";
          li.style.backgroundColor = "burlywood";
          li.style.borderRadius = "5px"
          li.style.cursor = "pointer";
          const span = document.createElement("span");
          span.textContent = tag;
          span.style.backgroundColor = "burlywood";
          span.style.borderRadius = "5px"
          li.addEventListener("click", imageTagOnClick);
          li.appendChild(span);
          ul.appendChild(li);
        });
      
        imageTagListDiv.appendChild(ul);
      }
      
    console.log("Cloudinary response:", cloudinaryData);
    setIsLoading(false)
    router.refresh()
  }


  const fetchPNGfromURL = async (imageUrl: string) => {
    setUploadedImageLoading(true);
    try {
      /*
      // Fetch the image data from the URL
      const response = await fetch(imageUrl);
      */

      // Make a request to your custom API endpoint
      const response = await fetch('/api/imageUrl-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      });

      if (!response.ok) {
        // Handle the case where the image could not be fetched
        props.showAlert(true, "Failed to fetch the image from the URL.");
        setUploadedImageLoading(false);
        return;
      }
      
      // Get the blob data for the image
      const imageBlob = await response.blob();

      console.log("imageBlob: ", imageBlob)

      // Create a Data URL for the image
      const imageUrlData = URL.createObjectURL(imageBlob);

      // Update the state to display the image
      setUploadedImage(imageUrlData);
      setUploadedImageBlob(imageBlob);
      setGhostImage(imageUrlData);
      (document.getElementById("descText") as HTMLTextAreaElement).value = "";
      setUploadedImageFile(null);
      setUploadedImageLoading(false);
      router.refresh()
    } catch (error) {
      console.error("Error fetching image from URL:", error);
      props.showAlert(true, "An error occurred while fetching the image from the URL.");
      setUploadedImageLoading(false);
    }
  };

  const cleanPNG = async (clearMaskedAreas: () => void) => {
    setIsLoading(true);
    setUploadedImage(null);
    setCldUploadImage(null);
    setUploadedImageBlob(null);
    setUploadedImageFile(null);
    setGhostImage(null);
    setIsLoading(false);
  
    if (typeof clearMaskedAreas === 'function') {
      clearMaskedAreas();
      setUploadedImage(null);
    }
  };

  const clearMaskedAreas = () => {
    if (uploadedImageFile !== null) {
      setUploadedImage(URL.createObjectURL(uploadedImageFile));
    } else if (uploadedImageBlob !== null) {
      console.log("clearMaskArea for ImageBlob: ", uploadedImageBlob);
      setUploadedImage(URL.createObjectURL(uploadedImageBlob));
    }
    const sizePlus = firstSize.current - 1;
    setUploadedImageSize(`${sizePlus}x${sizePlus}`);
  };

  const uploadPNG = (e: ChangeEvent<HTMLInputElement>) => {    
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.type !== "image/png") {
        props.showAlert(true, "File you uploaded is not in a valid format. Upload an image/png file.");
      } else if (file.size >= 4000000) {
        props.showAlert(true, "Upload an image/png file less than 4MB.");
      } else {        
        const img = new Image();
        img.src = URL.createObjectURL(file);
        img.onload = () => {
          if (img.width !== img.height) {
            props.showAlert(true, "Image you uploaded is not a square. Upload a square image.");
          } else {            
            setUploadedImage(URL.createObjectURL(file));
            setUploadedImageSize(`${img.width}x${img.height}`);
            firstSize.current = img.width;
            setUploadedImageFile(file);
            setGhostImage(URL.createObjectURL(file));
            (document.getElementById("descText") as HTMLTextAreaElement).value = "";
            setUploadedImageBlob(null);
            router.refresh()
          }
        }
      }
    }
  }

  const firstSize = useRef<number>(512);

  useEffect(() => {
    if (uploadedImageSize === "255x255" || uploadedImageSize === "511x511" || uploadedImageSize === "1023x1023") {
      setUploadedImageSize(`${firstSize.current}x${firstSize.current}`);
    }
  }, [uploadedImageSize]);

  useEffect(() => {
    const generateButton = document.getElementById("generate") as HTMLButtonElement | null;
    if (generateButton) {
      //generateButton.disabled = uploadedImage === null;
      generateButton.disabled = text === "";
    }
  }, [uploadedImage, text]);


  useEffect(() => {
    const size1Button = document.getElementById("size1") as HTMLButtonElement | null;
    if (size1Button) {
      size1Button.disabled = true;
    }
  }, []);


  const generateImage = async (count: number, size: string, text: string) => {
    const myCanvas = document.getElementById("myCanvas") as HTMLCanvasElement | null;
    let parsedData: ParsedData = {};
    let formData: FormData | null = null;

    const userMessage: UIMessage = {
      id: companion.id,
      role: "user",
      parts: [{ type: 'text', text: text }],
    };

    const assistantMessage: UIMessage = {
      id: companion.id,
      role: "assistant",
      parts: [{ type: 'text', text: text }],
    };

    setMessages([...messages, userMessage, assistantMessage])

    if (myCanvas) {
      const dataURL = myCanvas.toDataURL();
      console.log("dataURL: ", dataURL)
      const response = await fetch(dataURL);
      console.log("response: ", response)
      const data = await response.blob();
      console.log("data: ", data)
      const metadata = {
        type: 'image/png'
      };

      // Image continuing edit
      if (
        count >= 1 &&
        (size === size256 || size === size512 || size === size1024) &&
          uploadedImageBlob !== null && uploadedImageFile === null &&
        text !== "" 
      ) {
        console.log("Debugging: count =", count);
        console.log("Debugging: size =", size);
        console.log("Debugging: uploadedImageBlob =", uploadedImageBlob);
        console.log("Debugging: text =", text);

        let maskFile = new File([data], "contEditMask.png", metadata);
        if (maskFile === null) {
          props.showAlert(true, "An error occurred during the transformation of the mask image. Try again later.");
        } else {
          setUploadedImageLoading(true);
          setUploadedImage(null);
          setIsCanvas(false);
          changeDisabilityOfAllButtons(true);
          setUploadedImageSize(size);

          formData = new FormData();
          formData.append('mode', "contEdit");
          formData.append('image', uploadedImageBlob);
          formData.append('size', size);
          formData.append('n', count.toString());
          formData.append('prompt', text);
          formData.append('mask', maskFile);
          console.log("Image upload: ", formData);
        }

      // Image first time edit
      } else if (
        count >= 1 &&
        (size === size256 || size === size512 || size === size1024) &&
          uploadedImageFile !== null && uploadedImageBlob === null &&
          text !== "" 
      ) {
        console.log("Debugging: count =", count);
        console.log("Debugging: size =", size);
        console.log("Debugging: uploadedImageFile =", uploadedImageFile);
        console.log("Debugging: text =", text);

        let maskFile = new File([data], "editMask.png", metadata);
        if (maskFile === null) {
          props.showAlert(true, "An error occurred during the transformation of the mask image. Try again later.");
        } else {
          setUploadedImageLoading(true);
          setUploadedImage(null);
          setIsCanvas(false);
          changeDisabilityOfAllButtons(true);
          setUploadedImageSize(size);

          formData = new FormData();
          formData.append('mode', "edit");
          formData.append('image', uploadedImageFile);
          formData.append('size', size);
          formData.append('n', count.toString());
          formData.append('prompt', text);
          formData.append('mask', maskFile);
          console.log("Image upload: ", formData);
        }

      // Image generation
      } else if (
        count >= 1 &&
        (size === size256 || size === size512 || size === size1024) &&
         text !== ""
      ) {
        setUploadedImageLoading(true);
        setUploadedImage(null);
        setIsCanvas(false);
        changeDisabilityOfAllButtons(true);
        formData = new FormData();
        formData.append('mode', "generate");
        formData.append('size', size);
        formData.append('n', count.toString());
        formData.append('prompt', text);

        console.log("Prompt only: ", formData);

      } else {
        props.showAlert(true, alertMessage);
      }
    }

    try {
      if (formData !== null) {
        const requestOptions = {
          method: 'POST',
          body: formData,
        };
        const response = await fetch('/api/image/dalle', requestOptions);

        /*if (!response.ok) {
          throw new Error('Request failed with status ' + response.status);
        }*/
        parsedData = await response.json();
        console.log("parsedData", parsedData);
      }
    } catch (error: any) {
      if (error?.response?.status === 403) {
        proModal.onOpen();
      } else if (error?.response?.status === 400) {
        const errorMessage = error?.response?.data?.message || "Bad Request";
        toast({
          variant: "destructive",
          description: errorMessage,
          duration: 3000,
        });
      } else {
        toast({
          variant: "destructive",
          description: "Something went wrong.",
          duration: 3000,
        });
      }   
    }

    if (
      Array.isArray(parsedData) &&
      parsedData.length > 0 &&
      parsedData[0].url !== undefined &&
      parsedData[0].url !== "" &&
      parsedData[0].url !== null
    ) {
      let imageUrl = parsedData[0].url;
      console.log("imageUrl: ", imageUrl)
      setCldUploadImage(imageUrl);
      await fetchPNGfromURL(imageUrl);
      setIsCanvas(true);
      changeDisabilityOfAllButtons(false);
      setCorrespondingSizeButtonDisabled(size);
      setUploadedImageLoading(false);
  
    /*await downloadImage(imageUrl)
      .then(() => console.log('Image downloaded successfully'))
      .catch((error) => console.error('Error downloading image:', error));
    */

    // Insert data to database
    /*
    await axios.post(
      "/api/talk",
      {
        companionId: companion.id,
        content: text,
        role: "user",
      }
    );
    */  
            
    } else if (parsedData.error !== null && parsedData.error !== undefined) {
      console.log(parsedData.error.message);
      props.showAlert(true, parsedData.error.message);
      setUploadedImageLoading(false);
      setIsCanvas(true);
      changeDisabilityOfAllButtons(false);
      setCorrespondingSizeButtonDisabled(size);
    
    }
    setText('')
  };


  const brushSizeChanged = (value: number[]) => {
    const numValue = value[0]
    setBrushSize(numValue)
  }

  function setIncreaseValue(e: React.MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
    // set allowable samllest image size, smilar to zoom-in image size from 200 to fit 512 canvas box
    if ( imageTransformMode === "resizeCrop" && granularValue >= 200) {
      setGranularValue(() => granularValue - 100);
    } else if ( imageTransformMode === "rotate") {
      setRotateValue(() => rotateValue + 10);      
    }
  }

  function setDecreaseValue(e: React.MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    // set allowable samllest image size,
    if ( imageTransformMode === "resizeCrop" && granularValue < 512) {
      // zoom-in image
      setGranularValue(() => granularValue + 100);
    } else if ( imageTransformMode === "resizeCrop" && granularValue >= 512) {  
      //zoom-out image
      setImageTransformMode("resizeScale")
      setGranularValue(() => granularValue + 1);
    } else if ( imageTransformMode === "rotate") {
      setRotateValue(() => rotateValue + 10);      
    }
    // use useEffect to call cldImageTransform
  }

  function setCWRotation(e: React.MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
    setImageTransformMode("rotate")
    setRotateValue(() => rotateValue + 10);
    // use useEffect to call cldImageTransform() or rotateImages()
  }

  function setCCWRotation(e: React.MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    setImageTransformMode("rotate")
    setRotateValue(() => rotateValue - 10);
    // use useEffect to call cldImageTransform or rotateImages()
  }

  function setFlipVertical(e: React.MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    setRotateValue(() => rotateValue + 180);
    // use useEffect to call cldImageTransform or rotateImages()
  }

  const handleMediaEditorExport = async (secureUrl: string) => {
    setIsLoading(true)
    await fetchPNGfromURL(secureUrl);
    setIsCanvas(true);
    console.log("Received Secure URL: ", secureUrl);
    setIsLoading(false)
  };

  const handleImageDownload = (imageUrl: string) => {
    if (imageUrl) {
      const filename = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
      fetch(imageUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
    }
  };

  return (
    <div>
      <div className={cn('grid grid-cols-12 gap-5 overflow-hidden')}>
        <div className='flex items-center justify-center col-span-12 lg:col-span-7 2xl:col-span-6'>
          {/* Content on the Left side */}
          <div id="imageTagList" className='fixed top-1 left-[20%] h-12 w-300 flex items-center justify-center p-2'></div>
          {ghostImage !== null && (
            <img src={ghostImage} alt="..." id="ghostimage" hidden />
          )}
          <ImageComponent
            image={uploadedImage}
            size={uploadedImageSize}
            loading={uploadedImageLoading}
            isCanvas={isCanvas}
            brushSize={brushSize}
          />          
          <div className='absolute bottom-1 2xl:bottom-[10%] flex flex-row justify-center items-center'>
            <div className='mt-1'>
              <MediaEditor onExport={handleMediaEditorExport} />
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <label
                    id="uploadpnglabel"
                    className="inline-block cursor-pointer text-white text-sm px-2 py-1"
                    style={{
                      backgroundColor: "transparent",
                      borderRadius: "4px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <ImagePlus color="cyan" className="h-5 w-5" />
                    <input type="file"
                      className="hidden"
                      id="uploadpng"
                      onChange={uploadPNG}
                      disabled={uploadedImageLoading || loading}
                    />
                  </label>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Upload Image (size must be 512x512)</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex cursor-pointer items-center text-white bg-transparent text-sm px-2 py-1" 
                    id="clearmask" 
                    type="button" 
                    onClick={clearMaskedAreas}
                    disabled={uploadedImageLoading || loading}
                  >
                    <RefreshCcw color="cyan" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Clear Mask</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1 w-[6rem] flex items-center'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Slider
                    min={1}
                    max={30}
                    defaultValue={[brushSize]}
                    id="slider"
                    onValueChange={(value) => brushSizeChanged(value)}
                    className="h-[90%]"
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Adjust Brush Size</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex items-center text-white bg-transparent text-sm px-2 py-1" 
                    id="cleanpng" 
                    type="button" 
                    onClick={() => cleanPNG(clearMaskedAreas)}
                    disabled={uploadedImageLoading || loading}
                  >
                    <ImageOff color="cyan" className="h-5 w-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>刪除底圖</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex items-center text-white bg-transparent text-sm p-1"
                    onClick={setCCWRotation}
                    disabled={!uploadedImage || !cldUploadImage}
                  >
                    <RotateCcw color="cyan" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>向左旋轉</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex items-center text-white bg-transparent text-sm p-1"
                    onClick={setCWRotation}
                    disabled={!uploadedImage || !cldUploadImage}
                  >
                    <RotateCw color="cyan" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>向右旋轉</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex items-center text-white bg-transparent text-sm px-2 py-1"
                    onClick={setFlipVertical}
                    disabled={!uploadedImage || !cldUploadImage}
                  >
                    <FlipVertical color="cyan" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>垂直旋轉</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Select                
                disabled={uploadedImageLoading || loading}
                onValueChange={(value) => setImageTransformMode(value)}
              >
                <SelectTrigger className="w-[6rem] px-1">
                  <SelectValue placeholder="調整圖像" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="resizeCrop">場景遠近</SelectItem>
                  <SelectItem value="rotate">旋轉圖像</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className='mt-1 w-[3rem]'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Switch
                    className='flex items-center bg-primary/10 space-x-2'
                    id="imageAutoKeep"
                    checked={imageAutoKeep}
                    onCheckedChange={(e) => setImageAutoKeep(e)}
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>自動收藏圖片</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className='mt-1'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex items-center text-white bg-transparent text-sm px-2 py-1" 
                    id="cldupload" 
                    type="button" 
                    onClick={() => cldUploadPNG()}
                    disabled={!cldUploadImage || uploadedImageLoading || loading}
                  >
                    <Heart color="cyan" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>收藏這張圖片</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
          <div className='mt-1'>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="flex items-center text-white bg-transparent text-sm px-2 py-1"
                  onClick={setIncreaseValue}
                  disabled={!uploadedImage || !cldUploadImage}
                >
                  <Plus color="cyan" className="w-5 h-5" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>增加</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="flex items-center text-white bg-transparent text-sm px-2 py-1"
                  onClick={setDecreaseValue}
                  disabled={!uploadedImage || !cldUploadImage}
                >
                  <Minus color="cyan" className="w-5 h-5" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>減少</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
        <div className='max-h-[88vh] col-span-12 lg:col-span-5 flex flex-col justify-end items-center'>
          {/* Content on the Center side */}
          <div className="flex-grow overflow-hidden">
            <div className="min-h-[65vh] max-h-[88vh] lg:min-h-[65vh] lg:max-h-[88vh] pb-100px overflow-y-auto">
              {messages.length ? (
                <>
                  <TalkMessages 
                    companion={companion}
                    isLoading={isLoading}
                    messages={messages}
                  />
                </>
              ) : (
                <EmptyImageScreen setInput={setInput} />
              )}
            </div>
            <div className="flex justify-end items-center h-2 px-2 m-2">
              {selectedMediaUrl && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button 
                      onClick={() => selectedMediaUrlClick(selectedMediaUrl)}
                      disabled={uploadedImageLoading || loading}
                    >
                      <ChevronsLeft className="h-5 w-5" style={{ color: '#36d7b7' }} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>使用這張圖為底圖</p>
                  </TooltipContent>
                </Tooltip>
              )}
              {loading || uploadedImageLoading ? (
                <div className="flex justify-center items-center">
                  <BarLoader width={330} color={'#36d7b7'} />
                </div>
              ) : null}
            </div>
          </div>
          <div className='my-1 gap-x-1 hidden'>
          <button 
              className="
                text-white 
                bg-primary/10 
                hover:bg-blue-800 
                font-small 
                rounded-lg 
                text-sm 
                mx-1               
                px-2 
                py-1 
                focus:outline-none
              " 
              id="size1" 
              type="button" 
              onClick={() => setSizeAndButtons(size256)}
            >
              256x256
            </button>
            <button 
              className="
                text-white 
                bg-primary/10 
                hover:bg-blue-800 
                font-small 
                rounded-lg 
                text-sm 
                mx-1 
                px-2 
                py-1 
                focus:outline-none
              " 
              id="size2" 
              type="button" 
              onClick={() => setSizeAndButtons(size512)}
            >
              512x512
            </button>
            <button 
              className="
                text-white 
                bg-primary/10 
                hover:bg-blue-800 
                font-small 
                rounded-lg 
                text-sm 
                mx-1 
                px-2 
                py-1 
                focus:outline-none
              " 
              id="size3" 
              type="button" 
              onClick={() => setSizeAndButtons(size1024)}
            >
              1024x1024
            </button>
          </div>
          <div className='border-t border-primary/10 my-1 p-1 gap-x-1' style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Textarea
              className="form-control border-0 min-h-[50px] w-[275px] resize-none rounded-lg bg-primary/10 m-1 px-4 py-2 focus-within:outline-none sm:text-sm"              
              id="descText"
              placeholder="Describe the image."
              value={text}
              onChange={textChanged}
              onKeyDown={(e) => enterPress(e)}
              disabled={uploadedImageLoading || loading}
            ></Textarea>
            <button
              type="button"
              id="generate"
              className="btn btn-success text-white bg-primary/10 hover:bg-blue-800 font-medium rounded-full text-sm mr-1 p-3 focus:outline-none"
              onClick={() => generateImage(count, size, text)}
              disabled={uploadedImageLoading || loading || text === ''}
            >
              <Send className="w-5 h-5" />
            </button>
            <Dialog modal={false} open={open} onOpenChange={setOpen} >
              <DialogTrigger asChild>
                <Button className="btn btn-success h-12 text-white bg-primary/10 hover:bg-blue-800 font-medium rounded-full text-sm mr-3 p-3 focus:outline-none"><MessageCircle /></Button>
              </DialogTrigger>
              <DialogContent onInteractOutside={(e: any) => e.preventDefault()} className="sm:max-w-[290px]">
                <DialogHeader>
                  <DialogTitle>Ask AI</DialogTitle>
                  <DialogDescription>
                    Ask AI to generate image prompt or idea for you.
                  </DialogDescription>
                </DialogHeader>
                <ChatForm 
                  isLoading={isLoading} 
                  input={input} 
                  handleInputChangeAction={handleInputChange}
                  onSubmitAction={onSubmit} 
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaskImage