"use client";

import { useBotStore } from "@/store/bot";
import { DefaultChatTransport } from "ai";
import { useChat } from "@ai-sdk/react";
import { useCallback, useEffect, useState } from "react";
import { useClientConfig } from "@/cl/app/components/ui/chat/hooks/use-config";

// Combine useChat and useBotStore to manage chat session
export function useChatSession() {
  const botStore = useBotStore();
  const bot = botStore.currentBot();
  const session = botStore.currentSession();
  const { updateBotSession } = botStore;

  const [isFinished, setIsFinished] = useState(false);
  const { chatAPI } = useClientConfig();
  console.log("chatAPI: ", chatAPI)
  const [input, setInput] = useState('');
  const {
    messages,
    setMessages,
    sendMessage,
    status,
    regenerate,    
    stop,
  } = useChat({
    transport: new DefaultChatTransport({
      api: '/api/llm',
      body: () => ({
        context: bot.context,
        modelConfig: bot.modelConfig,
        datasource: bot.datasource,
      }),      
    }),

    onError: (error: unknown) => {
      if (!(error instanceof Error)) throw error;
      const message = JSON.parse(error.message);
      alert(message.detail);
    },

    onFinish: () => setIsFinished(true),

  });

  const isLoading = status !== "ready" && status !== "error";

  // load chat history from session when component mounts
  const loadChatHistory = useCallback(() => {
    setMessages(session.messages);
  }, [session, setMessages]);

  // sync chat history with bot session when finishing streaming
  const syncChatHistory = useCallback(() => {
    if (messages.length === 0) return;
    updateBotSession((session) => (session.messages = messages), bot.id);
  }, [messages, updateBotSession, bot.id]);

  useEffect(() => {
    loadChatHistory();
  }, [loadChatHistory]);

  useEffect(() => {
    if (isFinished) {
      syncChatHistory();
      setIsFinished(false);
    }
  }, [isFinished, setIsFinished, syncChatHistory]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage({ text: input });
    setInput('');
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  return {
    messages,
    input,
    isLoading,
    handleSubmit,
    handleInputChange,
    regenerate,
    stop,
    sendMessage,
    setInput,
  };
}
