"use client"

import Image from "next/image";
import { ComponentRef, useEffect, useRef, useState, Dispatch, SetStateAction } from "react";
import { type UIMessage } from 'ai'

import { Separator } from '@/components/ui/separator'
import { Companion } from '@prisma/client'
import { ImageMessage } from '@/components/image-message'
import ImageModal from "@/components/modals/ImageModal";

type SetFormData = Dispatch<SetStateAction<FormData>>;

interface ImageData  {
  type: string;
  content: string;
  url: string;
}

export interface ImageList {
  companion?: Companion
  isLoading?: boolean
  messages: UIMessage[]
  imageData?: ImageData[] | undefined
  formData: FormData
  setFormData: SetFormData
  selectedMediaUrl: string | null
  setChatWithVisionImage: Dispatch<SetStateAction<string>>
  path?: string
}

export function ImageList({
  companion,
  isLoading,
  messages,
  imageData,
  formData,
  setFormData,
  selectedMediaUrl,
  setChatWithVisionImage,
  path,
}: ImageList) {
  if (!messages.length) {
    return null
  }
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [imageUrls, setImageUrls] = useState<ImageData[]>([]);
  const scrollRef = useRef<ComponentRef<"div">>(null)
  const [fakeLoading, setFakeLoading] = useState(messages.length === 0 ? true : false)
  /*const greetMessage: Message = {
    id: companion.id,
    role: "assistant",
    content: `您好, 我是 ${companion.name}, ${companion.description}`,
  };*/

  useEffect(() => {
    if (imageData && imageData.length > 0) {
      setImageUrls(imageData);
    }
  }, [imageData]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setFakeLoading(false);
    }, 1000);

    return () => {
      clearTimeout(timeout);
    }
  }, []);
  
  useEffect(() => {
    scrollRef?.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages.length])

  /*useEffect(() => {
  console.log("imageData@ImageList: ", imageData)
  console.log("imageUrls@ImageList: ", imageUrls)
  }, [imageData, imageUrls])*/

  return (
    <div className="relative mx-auto max-w-4xl px-4">
      {/*<ChatMessage
        isLoading={fakeLoading}
        src={companion.src}
        message={greetMessage}
      /> */}
      {messages.map((message, index) => (
        <div key={index}>
          <ImageMessage
            companionId={companion?.id!}
            isLoading={isLoading}
            src={companion?.src}
            message={message}
            formData={formData} 
            setFormData={setFormData}
            selectedMediaUrl={selectedMediaUrl}
            setChatWithVisionImage={setChatWithVisionImage}
            path={path}
          />
          {index < messages.length - 1 && (
            <Separator className="my-0 md:my-0 bg-muted/50" />
          )}
        </div>
      ))}
      {imageUrls && imageUrls.length > 0 && (
        <div className="flex justify-center mx-auto space-x-1">
          {imageUrls.map((data, index) => (
            data?.type === 'image' && (
              <div key={index} className="flex aspect-square relative h-[10rem] w-[10rem] sm:rounded-lg overflow-hidden">
                <ImageModal src={data.url} isOpen={selectedImageIndex === index} onClose={() => setSelectedImageIndex(null)} />
                <Image
                  onClick={() => setSelectedImageIndex(index)}
                  fill
                  src={data.url}
                  alt="Image"
                  height="100"
                  width="100"
                  className="object-cover cursor-pointer hover:scale-110 rounded-lg transition translate"
                />
              </div>
            )
          ))}
        </div>
      )}
      <div ref={scrollRef} />
    </div>
  )
}
