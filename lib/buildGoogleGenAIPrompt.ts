import { UIMessage } from 'ai';

export const buildGoogleGenAIPrompt = (messages: UIMessage[]) => ({
  contents: messages
    .filter(message => message.role === 'user' || message.role === 'assistant')
    .map(message => ({
      role: message.role === 'user' ? 'user' : 'model',
      parts: message.parts,
    })),
});

export const buildGoogleGenAIHistory = (messages: UIMessage[]) => ({
  history: messages
    .filter(message => message.role === 'user' || message.role === 'assistant')
    .map(message => ({
      role: message.role === 'user' ? 'user' : 'model',
      parts: message.parts,
    })),
});